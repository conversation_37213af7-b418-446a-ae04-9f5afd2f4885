<?php

namespace App\Filament\Admin\Resources\CmsResource\Pages;

use App\Filament\Admin\Resources\CmsResource;
use App\Filament\CrudDefaults;
use App\Models\Cms;
use App\Services\CmsService;
use App\Services\TranslationApiService;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Components\View;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Exceptions\Halt;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class EditCms extends EditRecord
{
    use CrudDefaults;

    protected static string $resource = CmsResource::class;

    public $showAutoTranslateModal = false;
    public $selectedLanguages = [];
    public $selectedFields = [];
    public $selectAllLanguages = false;
    public $selectAllFields = false;
    public $currentStep=1;
    public $translatedFields = [
        "url" => "URL",
        "title" => "Title",
        "metaTitle" => "Meta title",
        "content" => "Content",
        "metaDescription" => "Meta Description",
    ];

    public function mount($record): void
    {
        parent::mount($record);
    }

    private function getLanguagesWithIsoKeys(): array
    {
        $originalLanguages = CmsResource::getLanguages();
        $apiService = app(TranslationApiService::class);
        try {
            $response = $apiService->getLanguages(['limit' => 200]);
            $mapped = [];
            foreach ($response['data'] ?? [] as $language) {
                if (isset($language['id'], $language['languageISO2']) && isset($originalLanguages[$language['id']])) {
                    $iso2 = strtolower($language['languageISO2']);
                    $mapped[$iso2] = $originalLanguages[$language['id']];
                }
            }
            return $mapped;
        } catch (\Throwable $e) {
            Log::error('Failed to remap languages to ISO2', ['error' => $e->getMessage()]);
            return $originalLanguages;
        }
    }

    private function getAvailableLanguages()
    {
        $languages = $this->getLanguagesWithIsoKeys();
        return array_keys(array_filter($languages, function ($code) {
            return strtolower($code) !== 'en';
        }, ARRAY_FILTER_USE_KEY));
    }


    private function getAllLanguages() {
        return $this->getLanguagesWithIsoKeys();
    }

    public function doTranslate()
    {
        if (count($this->selectedFields) == 0 || count($this->selectedLanguages) == 0){
            Notification::make()
                ->danger()
                ->title('Error')
                ->body('No language selected')
                ->send();
            return;
        } else {
            $autotranslationSrevice = app(TranslationApiService::class);
            $data = $this->form->getState();
            unset($data['style']);
            $allBody = [];

            foreach ($this->selectedFields as $column) {
                if($column == 'url') continue;
                $allBody[] = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => "ContentManagementSystem",
                    'rowId'      => $this->record->id,
                    'columnName' => $column,
                ];
            }

            // check if url is selected set value in translation to url
            if(in_array("url", $this->selectedFields)) {
                $urlBody = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => 'ContentManagementSystem',
                    'rowId'      => $this->record->id,
                    'columnName' => 'url',
                ];
                $this->urlAutoTranslationProcess($urlBody);
            }

            if ($this->selectAllLanguages) {
                foreach($allBody as $body) {
                    $autotranslationSrevice->autoTranslate($body);
                }
            } else {
                foreach($allBody as $body) {
                    $autotranslationSrevice->autoTranslate($body, $this->selectedLanguages);
                }
            }

            Notification::make()
                    ->success()
                    ->title('Translation Updated')
                    ->send();
        }

    }

    public function urlAutoTranslationProcess($body)
    {
        $translationService = app(TranslationApiService::class);
        $cmsService = app(CmsService::class);

        // Get URL before auto-translation
        $urlBefore = $translationService->getResource(
            1,
            'ContentManagementSystem',
            'url',
            $this->record->id
        );

        // Run auto-translation
        $translationService->autoTranslate($body, $this->selectedLanguages);

        // Get URL after auto-translation
        $urlAfter = $translationService->getResource(
            1,
            'ContentManagementSystem',
            'url',
            $this->record->id
        );

        if (!empty($urlBefore['translations']) && !empty($urlAfter['translations'])) {
            $beforeTranslations = $this->transformTranslationsResult($urlBefore['translations']);
            $afterTranslations = $this->transformTranslationsResult($urlAfter['translations']);

            foreach ($this->selectedLanguages as $lang) {
                if (
                    isset($beforeTranslations[$lang]['translation'], $afterTranslations[$lang]['translation']) &&
                    $beforeTranslations[$lang]['translation'] !== $afterTranslations[$lang]['translation']
                ) {
                    $cmsService->createCMSRedirectionUrl(
                        $this->record->id,
                        $beforeTranslations[$lang]['translation'],
                        $beforeTranslations[$lang]['languageId']
                    );
                }
            }
        }
    }

    private function transformTranslationsResult(array $data): array
    {
        $result = [];

        foreach ($data as $entry) {
            $iso = $entry['language']['languageISO2'];
            $result[$iso] = [
                'languageId' => $entry['languageId'],
                'translation' => $entry['translation'] ?? $entry['autoTranslatedValue'] ?? ''
            ];
        }

        return $result;
    }

    protected function performUrl($url) {
        if ($url && strpos($url ?? '', '/{lang}/') !== 0) {
            $url = '/{lang}/' . $url;
        }
        return $url;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        if (empty($data['lang'])) {
            $data['lang'] = 1;
        }
        $url = $data['url'];
        if (!empty($url) && strpos($url ?? '', '/{lang}/') === 0) {
            $data['url'] = str_replace('/{lang}/', '', $url);
        }
        $cmsService = app(CmsService::class);

        $oldUrlQueryParams = [
            'where' => json_encode([
                'cmsId' => [
                    'value' => $data["id"],
                    'operator' => '=='
                ],
                'languageId' => [
                    'value' => 1,
                    'operator' => '=='
                ]
            ]),
            'fields' => json_encode(['id', 'url']),
        ];

        $oldUrlResponse = $cmsService->getCMSRedirectionUrlV4($oldUrlQueryParams);
        if(!empty($oldUrlResponse['data'])){
            $data['oldUrl'] = $oldUrlResponse['data'];
            foreach ($data['oldUrl'] as &$item) {
                $item['url'] = str_replace('/{lang}/', '', $item['url']);
            }
            $data['oldUrlIds'] = array_column($data['oldUrl'], 'id');
        }
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $data['status'] = (int) $data['status'];
        $data['lastChangedBy'] = auth()->user()->id;
        $languageId = (int) $data['lang'];


        if ($languageId != 1) {
            $translatableFields = [
                'title',
                'url',
                'content',
                'metaTitle',
                'metaDescription',
            ];

            $language = resolve(TranslationApiService::class)->getLanguage($languageId);
            $languageISO2 = $language['languageISO2'];
            $languageId = $language['id'];
            $cmsTranslationRequestBody = [
                "meta" => [
                    [
                        "apiId" => config('services.cms_api.api_id'),
                        "tableName" => "ContentManagementSystem",
                        "accessor" => "data",
                        "rowIds" => [
                            $record['id']
                        ]
                    ]
                ]
            ];
            $translatedCmsItemResponse = resolve(TranslationApiService::class)->cmsTranslationTranslate($languageISO2, $cmsTranslationRequestBody);

            if (isset($translatedCmsItemResponse[config('services.cms_api.api_id')])) {
                $translatedCmsItemData = $translatedCmsItemResponse[config('services.cms_api.api_id')]['ContentManagementSystem'][$record['id']];
                foreach ($translatableFields as $field) {
                    $value = isset($translatedCmsItemData[$field]) ? $translatedCmsItemData[$field] : '';
                    $record[$field] = $value;
                }
            }
        }

        $data['url'] = $this->performUrl($data['url']);

        $cmsService = app(CmsService::class);
        $translationService = app(TranslationApiService::class);

        // redirect url process
        if (isset($data['url']) && $record['url'] != $data['url']) {
            $cmsService->createCMSRedirectionUrl($record['id'], $record['url'], $languageId);

            // update in translation db
            $translatedUrlBody = [
                'apiId'      => config('services.cms_api.api_id'),
                'tableName'  => "ContentManagementSystem",
                'rowId'      => $record['id'],
                'columnName' => "url",
                'type'       => "string",
                'translations' => [
                    [
                        'languageId'  => (int) $data['lang'],
                        'translation' => $data['url']
                    ]
                ],
                'translateAll' => false,
            ];
            $translationService->saveResource($translatedUrlBody);
        }
        unset($data['oldUrl']);

        if ($data['lang'] == 1) {
            unset($data['lang']);
            $response = $cmsService->updateContentManagementSystem($record->id, $data);
            if (isset($response['affected'])) {
                Cms::bustSushiCache();
                return $record->fill($response);
            } else {
                $this->halt();
                return $record;
            }
        } else {
            $allBody = [];
            unset($data['style']);
            if ($record['title'] != $data['title']) {
                $allBody[] = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => "ContentManagementSystem",
                    'rowId'      => $record['id'],
                    'columnName' => "title",
                    'type'       => "string",
                    'translations' => [
                        [
                            'languageId'  => (int) $data['lang'],
                            'translation' => $data['title']
                        ]
                    ],
                    'translateAll' => false,
                ];
            }
            if ($record['metaTitle'] != $data['metaTitle']) {
                $allBody[] = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => "ContentManagementSystem",
                    'rowId'      => $record['id'],
                    'columnName' => "metaTitle",
                    'type'       => "string",
                    'translations' => [
                        [
                            'languageId'  => (int) $data['lang'],
                            'translation' => $data['metaTitle']
                        ]
                    ],
                    'translateAll' => false,
                ];
            }
            if ($record['content'] != $data['content']) {
                $allBody[] = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => "ContentManagementSystem",
                    'rowId'      => $record['id'],
                    'columnName' => "content",
                    'type'       => "html",
                    'translations' => [
                        [
                            'languageId'  => (int) $data['lang'],
                            'translation' => $data['content']
                        ]
                    ],
                    'translateAll' => false,
                ];
            }
            if ($record['metaDescription'] != $data['metaDescription']) {
                $allBody[] = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => "ContentManagementSystem",
                    'rowId'      => $record['id'],
                    'columnName' => "metaDescription",
                    'type'       => "string",
                    'translations' => [
                        [
                            'languageId'  => (int) $data['lang'],
                            'translation' => $data['metaDescription']
                        ]
                    ],
                    'translateAll' => false,
                ];
            }
            if(empty($allBody)) {
                return $record;
            }
            foreach ($allBody as $body) {
                $response = $translationService->saveResource($body);
            }
            if ($response) {
                return $record->fill($response);
            } else {
                Notification::make()
                    ->danger()
                    ->title('Error')
                    ->body('Failed to translate CMS.')
                    ->send();
                $this->halt();
                return $record;
            }
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('RedirectionUrl')
                ->label('Add Redirection Url')
                ->modalHeading('New Redirection Url')
                ->modalWidth('md')
                ->form([
                    Forms\Components\TextInput::make('url')
                        ->label('URL')
                        ->required()
                        ->columnSpanFull(),
                ])->action(function (array $data, $livewire) {
                    $form = $livewire->form;
                    $data['url'] = $this->performUrl($data['url']);
                    $cmsService = app(CmsService::class);
                    $languageId = (int) $form->getState()['lang'];
                    $response = $cmsService->createCMSRedirectionUrl($this->record['id'], $data['url'], $languageId);
                    if($response == null) {
                        Notification::make()
                            ->danger()
                            ->title('Error')
                            ->body('Redirection Url Already Exist')
                            ->send();
                        throw new Halt();
                    }
                    $oldUrl  = $form->getState()['oldUrl'];
                    $oldUrl[] = ['url' => $data['url']];
                    // TODO HAVING BUG TO BE RESOLVE
                    $form->fill([
                        ...$form->getState(),
                        'oldUrl' => $oldUrl
                    ]);
                }),
            Actions\Action::make('autotranslate')
                ->label('Autotranslation')
                ->modalHeading('Autotranslate CMS')
                ->form([
                    View::make('filament.forms.components.cms-auto-translation-modal')
                        ->viewData([
                            'languages' => $this->getLanguagesWithIsoKeys(),
                            'selectAllLanguages' => $this->selectAllLanguages,
                            'selectedLanguages' => $this->selectedLanguages,
                            'translatedFields' => $this->translatedFields,
                            'selectAllFields' => $this->selectAllFields,
                            'selectedFields' => $this->selectedFields,
                            'showAutoTranslateModal' => $this->showAutoTranslateModal,
                            'currentStep' => $this->currentStep,
                        ]),
                ])
                ->modalSubmitAction(false)
                ->modalCancelAction(false),
        ];
    }

    protected function getRedirectUrl(): string
    {
        // Stay on the same edit page after save
        return $this->getResource()::getUrl('edit', ['record' => $this->record->getKey()]);
    }

    public function setCurrentStep($stepNumber) {
        if($this->currentStep < $stepNumber && count($this->selectedFields) == 0) {
            Notification::make()
                ->danger()
                ->title('Error')
                ->body('No field selected')
                ->send();
            return;
        }
        $this->currentStep = $stepNumber;
    }
}
