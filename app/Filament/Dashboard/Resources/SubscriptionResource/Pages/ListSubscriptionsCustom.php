<?php

namespace App\Filament\Dashboard\Resources\SubscriptionResource\Pages;

use App\Filament\Dashboard\Resources\SubscriptionResource;
use App\Services\PlanService;
use App\Services\TransactionService;
use App\Services\PaymentProviders\PaymentService;
use App\Constants\PaymentProviderConstants;
use App\Constants\SubscriptionStatus;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Models\Subscription;
use Carbon\Carbon;

class ListSubscriptionsCustom extends ListRecords
{
    protected static string $resource = SubscriptionResource::class;

    protected static string $view = 'filament.dashboard.pages.custom.subscription-list';

    public $planList;

    public bool $showModal = false;

    public bool $showNextSubModal = false;

    public $checkoutUrl = null;

    public $cards = [];

    public $selectedCardId = null;

    public $loadingElement = false;

    public $transactionUser = [];

    public $nextSubscription = null;

     protected function loadNextSubscription(): void
    {
        // Look for a scheduled subscription that will start after the current one ends
        // This could be a subscription with status 'scheduled' or 'pending'
        // Adjust the status values based on your system's implementation
        $this->nextSubscription = Subscription::where('user_id', auth()->id())
            ->whereIn('status', [
                SubscriptionStatus::ACTIVE->value,
            ])
            ->where('starts_at', '>', Carbon::now('UTC'))
            ->with(['plan.product', 'plan.meter', 'currency', 'discounts'])
            // ->orderBy('starts_at', 'asc')
            ->first();

    }

    public function getHeaderActions(): array
    {
        return [];
    }

    public function getTitle(): string
    {
        return '';
    }

    public function mount(): void
    {
        $this->loadNextSubscription();
        $this->transactionUser = app(TransactionService::class)->getCurrentUserTransaction();

        $paymentProviderStrategy = app(PaymentService::class)->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        $user = auth()->user();

        if($user){
            $this->cards = $paymentProviderStrategy->getPaymentMethods($user);
            $this->selectedCardId = collect($this->cards)->firstWhere('is_default', true)['id'] ?? null;
        }
        // dd($this->cards);
        $this->checkoutUrl = config('services.base_url.app');
        $dataSub = Subscription::query()
            ->where('user_id', auth()->user()->id)
            ->where('status', 'active')
            ->first();

        if($dataSub){
            $this->checkoutUrl = $this->checkoutUrl.'/checkout/change/';
        }else{
            $this->checkoutUrl = $this->checkoutUrl.'/checkout/plan/';
        }


        $plans = app(PlanService::class)->getAllActivePlans();
        $this->planList = $this->reArrangePlan($plans);
    }

    public function confirmCardChange($cardId)
    {
        $user = auth()->user();

        $paymentProviderStrategy = app(PaymentService::class)->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        /** @var StripeProvider $stripeProvider */
        $stripeProvider = $paymentProviderStrategy;

        try {
            $stripeProvider->updateDefaultPaymentMethod($user, $cardId);
        } catch (ApiErrorException $e) {
            logger()->error('An error occurred while updating the default card');
            Log::error('An error occurred while updating the default card: ' . $e->getMessage());
        }
    }

    public function cancelSubscription()
    {
        $activeSubscription = $this->getActiveSubscriptionProperty();

        if (!$activeSubscription) {
            $this->notify('error', 'No active subscription found to cancel.');
            return;
        }

        $subscriptionService = app(\App\Services\SubscriptionService::class);
        $paymentProviderStrategy = app(\App\Services\PaymentProviders\PaymentService::class)
            ->getPaymentProviderBySlug(\App\Constants\PaymentProviderConstants::STRIPE_SLUG);

        if (!$subscriptionService->canCancelSubscription($activeSubscription)) {
            $this->notify('error', 'You cannot cancel this subscription.');
            return;
        }

        $reason = '';
        $additionalInfo = null;

        $subscriptionService->cancelSubscription(
            $activeSubscription,
            $paymentProviderStrategy,
            $reason,
            $additionalInfo
        );

        $this->notify('success', 'Your subscription has been successfully cancelled.');

        $this->redirect(\App\Filament\Dashboard\Resources\SubscriptionResource::getUrl());
    }


    public function getActiveSubscriptionProperty()
    {

        return Subscription::query()
            ->where('user_id', auth()->user()->id)
            ->where('status', 'active')
            ->whereDate('ends_at', '>', now())
            ->first();
    }


    private function reArrangePlan($plans){
        $productsProfessional = [];
        $productsParticular = [];

        foreach ($plans as $plan) {
            $product = $plan->product;

            $features = $product->features->map(function ($feature) {
                return [
                    'label' => $feature->name,
                    'key' => $feature->key,
                    'enabled' => $feature->pivot->enabled ?? false,
                    'ui_order' => $feature->ui_order
                ];
            })->toArray();

            $priceValue = 0;
            foreach ($plan->prices as $price) {
                if ($price->currency_id == 30 && $price->getOriginal('price')) {
                    $priceValue = $price->getOriginal('price') / 100;
                    break;
                }
            }

            $productData = [
                'name' => $plan->name,
                'price' => $priceValue,
                'slug' => $plan->slug,
                'description' => $plan->description ?? $product->description,
                'dossier' => $plan->user_count ?? 1,
               'type' => $plan->interval?->name === "month" ? "Monthly" : "Yearly",
                'account_type' => $product->account_type,
                'user_count' => $plan->user_count,
                'recommande' => $product->is_popular == 1,
                'features' => $features,
            ];

            if ($product->account_type === "Professionnel") {
                $productsProfessional[] = $productData;
            } elseif ($product->account_type === "Particulier") {
                $productsParticular[] = $productData;
            }
        }

        return [
            'productsProfessional' => $productsProfessional,
            'productsParticular' => $productsParticular,
        ];
    }

}
