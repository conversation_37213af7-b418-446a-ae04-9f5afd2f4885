<?php

namespace App\Filament\Dashboard\Pages;

use App\Services\CurrencyService;
use App\Services\TranslationApiService;
use App\Services\LanguageService;
use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;

class MySettings extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationLabel = 'My Settings';
    protected static ?string $slug = 'my-settings';
    protected static bool $shouldRegisterNavigation = true;
    protected static string $view = 'filament.dashboard.pages.my-settings';

    public $base_language;
    public $base_currency;

    protected TranslationApiService $translationService;
    protected LanguageService $languageService;
    protected CurrencyService $currencyService;

    // public function __construct()
    // {
    //     // parent::__construct();

    //     // Initialize with try-catch to avoid service issues during boot
    //     try {
    //         $this->translationService = app(TranslationApiService::class);
    //     } catch (\Throwable $e) {
    //         report($e);
    //         // $this->translationService = null;
    //     }
    // }

    public function boot(
        LanguageService $languageService,
        TranslationApiService $translationService,
        CurrencyService $currencyService
    ) {
        $this->translationService = $translationService;
        $this->languageService = $languageService;
        $this->currencyService = $currencyService;
    }

    public function mount(): void
    {
        $user = Auth::user();
        $settings = $user->settings ?? [];

        $this->form->fill([
            'base_language' => $settings['base_language'] ?? null,
            'base_currency' => $settings['base_currency'] ?? null,
        ]);
    }

    protected function getFormSchema(): array
    {
        $languageOptions = [];
        if ($this->translationService) {
            try {
                $res = $this->translationService->getAllLanguages(['limit' => 200]);
                $languages = Arr::get($res, 'data', []);

                // Map language code => language name
                $languageOptions = collect($languages)->mapWithKeys(function ($lang) {
                    $fullLanguageName = sprintf('%s (%s)', htmlspecialchars($lang['languageFullNative']), strtoupper($lang['languageISO2'])) ; // Sanitize
                    $flagCode = isset($lang['flagCode']) ? htmlspecialchars($lang['flagCode']) : null;

                    $labelHtml = $fullLanguageName;
                    if ($flagCode) {
                        $labelHtml = "<span class='flag-icon flag-icon-".strtolower($flagCode)." rounded mr-2'></span>" . $fullLanguageName;
                    }
                    $code = $lang['languageISO2'] ?? null;
                    $name = $labelHtml ?? null;

                    return $code && $name ? [$code => $name] : [];
                })->toArray();
            } catch (\Throwable $e) {
                report($e);
            }
        }

        $currencyOptions = [];
        if ($this->currencyService) {
            try {
                $currencies = $this->currencyService->getAllCurrencies();

                // Map currency code => currency name
                $currencyOptions = collect($currencies)->mapWithKeys(function ($curr) {
                    $code = $curr['code'] ?? null;
                    $name = sprintf('%s (%s)', $curr['name'], $curr['symbol']) ?? null;

                    return $code && $name ? [$code => $name] : [];
                })->toArray();
            } catch (\Throwable $e) {
                report($e);
            }
        }

        return [
            Select::make('base_language')
                ->label(t('core.base_language'))
                ->searchable()
                ->allowHtml()
                ->options($languageOptions)
                // ->getSearchResultsUsing(function (string $search) use ($languageOptions) {
                //     // Normalize invisible characters that Filament might be stuck with
                //     $normalizedSearch = trim($search);

                //     if ($normalizedSearch === '' || $normalizedSearch === chr(0) || $normalizedSearch === chr(31)) {
                //         return $languageOptions;
                //     }

                //     return collect($languageOptions)
                //         ->filter(function ($label) use ($normalizedSearch) {
                //             $name = explode('</span>', $label)[1] ?? $label;
                //             return str_contains(strtolower($name), strtolower($normalizedSearch));
                //         })
                //         ->mapWithKeys(fn ($label, $key) => [$key => $label])
                //         ->all();
                // })
                // ->getOptionLabelUsing(fn ($value) => $languageOptions[$value] ?? $value)
                ->required()
                ->extraAttributes([
                    'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                ]),

            Select::make('base_currency')
                ->label(t('core.base_currency'))
                ->options($currencyOptions)
                ->required()
                ->allowHtml()
                ->searchable()
                ->extraAttributes([
                    'class' => 'w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[1px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                ]),
        ];
    }

    public function submit()
    {
        $data = $this->form->getState();
        $user = Auth::user();
        $settings = $user->settings ?? [];

        $settings['base_language'] = $data['base_language'];
        $settings['base_currency'] = $data['base_currency'];

        // logger($data['base_language']);
        $this->languageService->updateLanguage($data['base_language']);

        $user->settings_json = $settings;
        $user->save();

        Notification::make()
            ->title(t('core.my_settings.updated_success'))
            ->success()
            ->send();

        return redirect()->route('filament.dashboard.pages.my-settings');
    }

    public static function getNavigationLabel(): string
    {
        return t('core.my_settings.title');
    }

    public function getTitle(): string | Htmlable
    {
        return '';
    }
}
