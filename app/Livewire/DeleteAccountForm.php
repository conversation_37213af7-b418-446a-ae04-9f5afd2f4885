<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use App\Mail\Cms\SendCmsMail;
use App\Services\ConfigService;
use Filament\Notifications\Notification;
use Jeffgreco13\FilamentBreezy\Livewire\MyProfileComponent;
use Illuminate\Support\Facades\Crypt;

class DeleteAccountForm extends MyProfileComponent
{

    private ConfigService $configService;

    public $confirming = false;

    private $storedDuration = 365;
    private $deletionDelay = 1;

    public function boot(ConfigService $configService): void
    {
        $this->configService = $configService;
        $this->storedDuration = (int)($this->configService->get('account.stored_duration') ?? 365);
        $this->deletionDelay = (int)($this->configService->get('account.delete_duration') ?? 1);

    }

    protected string $view = 'livewire.delete-account-form';


    public function openConfirmationModal()
    {
        $this->dispatch('open-modal', id: 'confirm-delete-account-modal');
    }

    public function closeConfirmationModal()
    {
        $this->dispatch('close-modal', id: 'confirm-delete-account-modal');
    }

    public function deleteAccount()
    {
        $user = Auth::user();
        if ($user) {
            $to_delete_at = now()->addDays($this->deletionDelay);
            $user->to_delete_at = $to_delete_at;
            $user->save();

            $lang = $user->settings["base_language"] ?? app()->getLocale() ?? 'en';
            $data = [
                'user_id' => $user->id,
                'expires_at' => now()->addHours(24)->timestamp,
            ];
            $token = Crypt::encrypt($data);
            $reactivationUrl = config("app.url") . '/reactivate-account?token=' . urlencode($token);
            $vars = [
                'name' => $user->name,
                'disabled_date' => $to_delete_at->format('d/m/Y'),
                'stored_duration' => $this->storedDuration,
                'limite_date' => $to_delete_at->format('d/m/Y'),
                'reactivation_url' => $reactivationUrl,
            ];

            // Send CMS-based email
            SendCmsMail::make()
                ->to($user->email)
                ->template('core.delete-account')
                ->lang(app()->getLocale() ?? $lang)
                ->vars($vars)
                ->send();

            Notification::make()
                ->title(__('Your account has been deleted.'))
                ->success()
                ->send();

            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();

            return redirect('/');
        }
        $this->closeConfirmationModal();
    }

    public static function getSort(): int
    {
        return 5;
    }
}
