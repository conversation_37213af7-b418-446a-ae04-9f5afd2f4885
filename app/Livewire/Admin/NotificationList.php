<?php

namespace App\Livewire\Admin;

use App\Models\MongoDatabaseNotification;
use App\Models\NotificationPreference;
use App\Models\NotificationSetting;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithoutUrlPagination;
use Livewire\WithPagination;

class NotificationList extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $unreadNotificationsCount = 0;
    public string $filter = 'all';
    public int $unreadCount = 0;
    public array $preferences = [];
    public array $preferenceTypes = [];

    public function mount(): void
    {
        $this->updateUnreadCount();
        $this->loadInitialPreferences();
    }

    public function loadInitialPreferences(): void
    {
        $allPreferences = NotificationSetting::all();
        $userChoices = auth()->user()->preferences()->get()->keyBy('key');



        foreach ($allPreferences as $preference) {
            $this->preferences[$preference->key] =
                $userChoices[$preference->key]->pivot->is_enabled ?? false;
        }

        $grouped = $allPreferences->groupBy('type');

        $this->preferenceTypes = $grouped
            ->map(function ($preferencesOfType) {
                return $preferencesOfType
                    ->map(function ($preference) {

                        return [
                            'key' => $preference->key,
                            'name' => $preference->name,
                        ];
                    })
                    ->all(); 
            })
            ->all(); 
    }

    public function savePreferences(array $newPreferences): void
    {
        $user = auth()->user();
        $preferenceIds = NotificationSetting::pluck('id', 'key');
        $syncData = [];

        foreach ($newPreferences as $key => $isEnabled) {
            if (isset($preferenceIds[$key])) {
                $syncData[$preferenceIds[$key]] = ['is_enabled' => (bool) $isEnabled];
            }
        }
        $user->preferences()->sync($syncData);
    }

    public function setFilter(string $newFilter): void
    {
        $this->filter = $newFilter;
        $this->resetPage();
    }

    public function updateUnreadCount(): void
    {
        $this->unreadCount = $this->getBaseQuery()->unread()->count();
    }

    public function markAsRead(string $notificationId): void
    {
        $notification = $this->getBaseQuery()->find($notificationId);
        if ($notification) {
            $notification->markAsRead();
            $this->updateUnreadCount();
        }
    }

    public function markAllAsRead(): void
    {
        $this->getBaseQuery()->unread()->get()->each->markAsRead();
        $this->updateUnreadCount();
    }


    public function render()
    {
        $query = $this->getBaseQuery();

        match ($this->filter) {
            'read' => $query->read(),
            'unread' => $query->unread(),
            default => null, 
        };

        $notifications = $query
            ->latest('created_at') 
            ->paginate(10); 

            
        return view('livewire.admin.notification-list', [
            'notifications' => $notifications,
        ]);
    }


    private function getBaseQuery()
    {
        $query = MongoDatabaseNotification::query();

        return $query;
    }
}