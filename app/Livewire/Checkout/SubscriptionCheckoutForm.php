<?php

namespace App\Livewire\Checkout;

use App\Constants\PaymentProviderConstants;
use App\Exceptions\LoginException;
use App\Exceptions\NoPaymentProvidersAvailableException;
use App\Exceptions\SubscriptionCreationNotAllowedException;
use App\Models\PaymentProvider;
use App\Services\CalculationService;
use App\Services\CheckoutService;
use App\Services\DiscountService;
use App\Services\LoginService;
use App\Services\PaymentProviders\PaymentService;
use App\Services\PlanService;
use App\Services\SessionService;
use App\Services\SubscriptionService;
use App\Services\UserService;
use App\Validator\LoginValidator;
use App\Validator\RegisterValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Models\Country;
use Stripe\StripeClient;
use App\Models\Address;

class SubscriptionCheckoutForm extends CheckoutForm
{
    private PlanService $planService;

    private SessionService $sessionService;

    private CalculationService $calculationService;

    private SubscriptionService $subscriptionService;

    public int $currentStep = 1;

    public $country = 'Madagascar';
    
    public $countryId;

    public $phoneCode = '+261';

    public $phoneNumber = '';

    public $name;

    public $email;

    public $city;

    public $isAdmin;

    public bool $loadingElementInCheckout = false;

    public $address;     

    public $countryListFromDb;

    public function boot(
        PlanService $planService,
        SessionService $sessionService,
        CalculationService $calculationService,
        SubscriptionService $subscriptionService,
        Request $request
    ) {
        $this->planService = $planService;
        $this->sessionService = $sessionService;
        $this->calculationService = $calculationService;
        $this->subscriptionService = $subscriptionService;
        $step = $request->query('step');

        if ($step && is_numeric($step) && $step >= 1 && $step <= 3) {
            $this->currentStep = (int)$step;
            $user = auth()->user();
            if(!$user && $this->currentStep == 3){
                // return redirect(config('services.base_url.dashboard').'/login');
                $this->currentStep = 2;
            }
        }

    }


    public function addPhone()
    {
        $this->phoneNumbers[] = ['code' => '+261', 'number' => ''];
    }

    public function getCountryListPropertyFromDb()
    {
        return Country::query()
        ->orderBy('name')
            ->get();
    }

    public function removePhone($index)
    {
        unset($this->phoneNumbers[$index]);
        $this->phoneNumbers = array_values($this->phoneNumbers);
    }

    public function redirectCustom()
    {
        $currentStep = request()->query('step');

        if ((int) $currentStep === 2) {
            return;
        }

        $newUrl = request()->fullUrlWithQuery(['step' => 2]);
        return redirect()->to($newUrl);
    }


    public function render(PaymentService $paymentService)
    {
        $subscriptionCheckoutDto = $this->sessionService->getSubscriptionCheckoutDto();
        $planSlug = $subscriptionCheckoutDto->planSlug;

        $plan = $this->planService->getActivePlanBySlug($planSlug);


        $totals = $this->calculationService->calculatePlanTotals(
            auth()->user(),
            $planSlug,
            $subscriptionCheckoutDto?->discountCode,
        );

        $canUserHaveSubscriptionTrial = $this->subscriptionService->canUserHaveSubscriptionTrial(auth()->user());

        $user = auth()->user();
        if($user){
            $this->name = $user['name'];
            $this->email = $user['email'];
            $this->isAdmin = $user['is_admin'];
            if ($user->address) {
                $this->countryId = $user->address->country_id; 
                if($user->address->country){
                    $this->country = $user->address->country->name;
                    $this->phoneCode = $user->address->country_code;
                }
                $this->address = $user->address->address_line_1;
                $this->city = $user->address->city;
                $this->phoneNumber = $user->address->phone;
            }
        }
        $clientSecret = null;
        if($user){
            $stripe = new \Stripe\StripeClient(config('services.stripe.secret_key'));

            /** @var StripeProvider $stripePaymentProvider */
            $stripePaymentProvider = $paymentService->getPaymentProviderBySlug(
                PaymentProviderConstants::STRIPE_SLUG
            );
            $paymentProcessParams = $this->subscriptionService->getPaymentProcessParams($plan, null);
            
            $oldSubscription = $this->subscriptionService->findCurrentActiveUserSubscription($user->id);
                $stripePaymentProvider->generateEmbeddedSessionArgs(
                    $paymentProcessParams,
                    $user,
                    $plan,
                    $oldSubscription
                )
            ;

            $defaultPaymentMethod = null;

            if ($user) {
                $stripe = $this->getClient();
                $stripeCustomerId = $this->findOrCreateStripeCustomer($user);
                $customer = $stripe->customers->retrieve($stripeCustomerId, []);
                $defaultPaymentMethod = $customer->invoice_settings->default_payment_method ?? null;
            }

            // dd($stripePaymentProvider->generateEmbeddedSessionArgs(
            //     $paymentProcessParams,
            //     $user,
            //     $plan,
            //     $oldSubscription
            // ));


            $checkoutSession = $stripe->checkout->sessions->create(
                $stripePaymentProvider->generateEmbeddedSessionArgs(
                    $paymentProcessParams,
                    $user,
                    $plan,
                    $oldSubscription
                )
            );

            $clientSecret = $checkoutSession->client_secret;
        }

        $priceValue = 0;
        foreach ($plan->prices as $price) {
            if ($price->currency_id == 30 && $price->getOriginal('price')) {
                $priceValue = $price->getOriginal('price');
                break;
            }
        }

        $this->countryListFromDb = $this->getCountryListPropertyFromDb();

       
        return view('livewire.checkout.subscription-checkout-form', [
            'userExists' => $this->userExists($this->email),
            'paymentProviders' => $this->getPaymentProviders(
                $paymentService,
                ! $canUserHaveSubscriptionTrial,
            ),
            'stripePublishableKey' => config('services.stripe.publishable_key'),
            'plan' => $plan,
            'priceValue' => $priceValue,
            'user' => $user ? $user->getOriginal() : null,
            'clientSecret' => $clientSecret,
            'totals' => $totals,
            'isAdmin' => $this->isAdmin,
            'isTrialSkipped' => ! $canUserHaveSubscriptionTrial,
        ]);
    }

    private function getClient(): StripeClient
    {
        return new StripeClient(config('services.stripe.secret_key'));
    }

    private function findOrCreateStripeCustomer(\App\Models\User $user): string
    {
        $stripe = $this->getClient();

        $stripeCustomerId = null;
        $stripeData = $user->stripeData();
        if ($stripeData->count() > 0) {
            $stripeData = $stripeData->first();
            $stripeCustomerId = $stripeData->stripe_customer_id;
        }

        if ($stripeCustomerId === null) {
            $customer = $stripe->customers->create(
                [
                    'email' => $user->email,
                    'name' => $user->name,
                ]
            );
            $stripeCustomerId = $customer->id;

            if ($stripeData->count() > 0) {
                $stripeData = $stripeData->first();
                $stripeData->stripe_customer_id = $stripeCustomerId;
                $stripeData->save();
            } else {
                $user->stripeData()->create([
                    'stripe_customer_id' => $stripeCustomerId,
                ]);
            }
        }

        return $stripeCustomerId;
    }

    public function saveUserAddress()
    {
        $user = auth()->user();

        $existingAddress = Address::where('user_id', $user->id)->first();

        if ($existingAddress) {
            $existingAddress->update([
                'country_id'   => $this->countryId,
                'address_line_1' => $this->address,
                'city'           => $this->city,
                'phone'          => $this->phoneNumber,
            ]);
        } else {
            Address::create([
                'user_id'        => $user->id,  
                'country_id'   => $this->countryId,
                'address_line_1' => $this->address,
                'city'           => $this->city,
                'phone'          => $this->phoneNumber,
            ]);
        }

        $currentUrl = url()->previous(); 
        $parsedUrl = parse_url($currentUrl);

        parse_str($parsedUrl['query'] ?? '', $query);
        $query['step'] = 3;

        $newUrl = $parsedUrl['path'] . '?' . http_build_query($query);

        return redirect()->to($newUrl);
    }



    public function checkout(
        LoginValidator $loginValidator,
        RegisterValidator $registerValidator,
        CheckoutService $checkoutService,
        PaymentService $paymentService,
        DiscountService $discountService,
        UserService $userService,
        LoginService $loginService,
    ) {
        try {
            parent::handleLoginOrRegistration($loginValidator, $registerValidator, $userService, $loginService);
        } catch (LoginException $exception) { // 2fa is enabled, user has to go through typical login flow to enter 2fa code
            return redirect()->route('login');
        }

        $subscriptionCheckoutDto = $this->sessionService->getSubscriptionCheckoutDto();
        $planSlug = $subscriptionCheckoutDto->planSlug;

        $plan = $this->planService->getActivePlanBySlug($planSlug);

        if ($plan === null) {
            return redirect()->route('home', ['lang' => app()->getLocale()]);
        }

        $paymentProvider = $paymentService->getPaymentProviderBySlug(
            $this->paymentProvider
        );

        $user = auth()->user();

        $discount = null;
        if ($subscriptionCheckoutDto->discountCode !== null) {
            $discount = $discountService->getActiveDiscountByCode($subscriptionCheckoutDto->discountCode);

            if (! $discountService->isCodeRedeemableForPlan($subscriptionCheckoutDto->discountCode, $user, $plan)) {
                // this is to handle the case when user adds discount code that has max redemption limit per customer,
                // then logs-in during the checkout process and the discount code is not valid anymore
                $subscriptionCheckoutDto->discountCode = null;
                $discount = null;
                $this->dispatch('calculations-updated')->to(SubscriptionTotals::class);
            }
        }

        try {
            $subscription = $checkoutService->initSubscriptionCheckout($planSlug);
        } catch (SubscriptionCreationNotAllowedException $e) {
            return redirect()->route('checkout.subscription.already-subscribed');
        }

        $initData = $paymentProvider->initSubscriptionCheckout($plan, $subscription, $discount);

        $subscriptionCheckoutDto->subscriptionId = $subscription->id;
        $this->sessionService->saveSubscriptionCheckoutDto($subscriptionCheckoutDto);

        if ($paymentProvider->isRedirectProvider()) {
            $link = $paymentProvider->createSubscriptionCheckoutRedirectLink(
                $plan,
                $subscription,
                $discount,
            );

            return redirect()->away($link);
        }

        $this->dispatch('start-overlay-checkout',
            paymentProvider: $paymentProvider->getSlug(),
            initData: $initData,
            successUrl: route('checkout.subscription.success'),
            email: $user->email,
            subscriptionUuid: $subscription->uuid,
        );
    }

    protected function getPaymentProviders(PaymentService $paymentService, bool $shouldSupportSkippingTrial = false)
    {
        if (count($this->paymentProviders) > 0) {
            return $this->paymentProviders;
        }

        $subscriptionCheckoutDto = $this->sessionService->getSubscriptionCheckoutDto();
        $planSlug = $subscriptionCheckoutDto->planSlug;

        $plan = $this->planService->getActivePlanBySlug($planSlug);

        $this->paymentProviders = $paymentService->getActivePaymentProvidersForPlan($plan, $shouldSupportSkippingTrial);

        if (empty($this->paymentProviders)) {
            logger()->error('No payment providers available for plan', [
                'plan' => $plan->slug,
            ]);

            throw new NoPaymentProvidersAvailableException('No payment providers available for plan'.$plan->slug);
        }

        if ($this->paymentProvider === null) {
            $this->paymentProvider = $this->paymentProviders[0]->getSlug();
        }

        return $this->paymentProviders;
    }
}
