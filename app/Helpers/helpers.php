<?php

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\AppModel;
use Illuminate\Support\Facades\Cookie;

if (! function_exists('localizedNumber')) {
    /**
     * Formats a number according to the application's current locale
     * or a specified locale, using the intl NumberFormatter.
     *
     * @param int|float $number The number to format.
     * @param string|null $locale The locale to use (e.g., 'en_US', 'de_DE'). Defaults to app()->getLocale().
     * @param int $style The NumberFormatter style (e.g., \NumberFormatter::DECIMAL, \NumberFormatter::CURRENCY).
     * @param int $maxFractionDigits For decimal style, the maximum number of fraction digits.
     * @return string|false The formatted number string, or false on error.
     */
    function localizedNumber(
        int | float $number,
        ?string $locale = null,
        int $style = \NumberFormatter::DECIMAL,
        int $maxFractionDigits = 2
    ): string | false {
        if (! extension_loaded('intl')) {
            if ($style === \NumberFormatter::DECIMAL) {
                $decimalsToShow = (floor($number) == $number) ? 0 : $maxFractionDigits;
                return number_format($number, $decimalsToShow);
            }
            return (string) $number;
        }

        $currentLocale = $locale ?? app()->getLocale();
        $formatter     = new \NumberFormatter($currentLocale, $style);

        if ($style === \NumberFormatter::DECIMAL) {
            if (floor($number) == $number) {
                $formatter->setAttribute(\NumberFormatter::MAX_FRACTION_DIGITS, 0);
            } else {
                $formatter->setAttribute(\NumberFormatter::MAX_FRACTION_DIGITS, $maxFractionDigits);
            }
        }

        return $formatter->format($number);
    }
}

if (!function_exists('t')) {
    /**
     * Translate the given message by fetching a JSON language file from a translation API.
     *
     * @param  string  $key The translation key (e.g., 'welcome', 'messages.new').
     * @param  array   $replace Placeholder replacements (e.g., [':name' => 'John']).
     * @param  string|null  $locale The target locale ID (e.g., '1' for English).
     * @param  int  $appId The application ID (e.g., 0 for common).
     * @return string Returns the translated string, or the key if not found/error.
     */
    function t($key, array $replace = [], $locale = null)
    {
        static $loadedLocaleFiles = [];

        $locale = app()->getLocale();

        $currentUrl = getDomainParts()["host"];

        $appId = AppModel::where('url', '=', $currentUrl)->first()->id ?? 0;

        $localeCacheKey = "{$appId}_{$locale}";

        if (!isset($loadedLocaleFiles[$localeCacheKey])) {
            $translationsForLocale = null;
            try {
                $baseApiUrl = rtrim(config('services.translation_api.url'), '/');

                if (!$baseApiUrl) {
                    Log::error("Translation API URL is not configured.");
                    $loadedLocaleFiles[$localeCacheKey] = '::API_ERROR::';
                } else {
                    $fullApiUrl = $baseApiUrl . sprintf("/interface-translation-value/json/%s/%s", $appId, $locale);
                    Log::debug("Translation API: Fetching translations from {$fullApiUrl}");

                    $httpClient = Http::acceptJson();

                    $response = $httpClient->get($fullApiUrl);

                    if ($response->successful()) {
                        $translationsForLocale = $response->json();
                        if (!is_array($translationsForLocale)) {
                            Log::warning("Translation API: Expected JSON object/array from {$fullApiUrl}, received: " . gettype($translationsForLocale));
                            $translationsForLocale = '::API_ERROR::';
                        }
                    } elseif ($response->status() == 404) {
                        Log::info("Translation API: Locale file not found for app '{$appId}', locale '{$locale}' at {$fullApiUrl}.");
                        $translationsForLocale = '::NOT_FOUND::';
                    } else {
                        Log::error("Translation API error for app '{$appId}', locale '{$locale}' at {$fullApiUrl}: " . $response->status(), [
                            'response_body' => $response->body()
                        ]);
                        $translationsForLocale = '::API_ERROR::';
                    }
                }
            } catch (\Illuminate\Http\Client\RequestException $e) {
                Log::error("Translation API request exception for app '{$appId}', locale '{$locale}': " . $e->getMessage());
                $translationsForLocale = '::API_ERROR::';
            } catch (\Exception $e) {
                Log::error("Generic error fetching translations for app '{$appId}', locale '{$locale}': " . $e->getMessage());
                $translationsForLocale = '::API_ERROR::';
            }
            $loadedLocaleFiles[$localeCacheKey] = $translationsForLocale;
        }


        $currentTranslations = $loadedLocaleFiles[$localeCacheKey];

        if ($currentTranslations === '::NOT_FOUND::' || $currentTranslations === '::API_ERROR::' || !is_array($currentTranslations)) {
            return $key;
        }

        $segments = explode('.', $key);
        $value = $currentTranslations;

        foreach ($segments as $segment) {
            if (is_array($value) && isset($value[$segment])) {
                $value = $value[$segment];
            } else {
                return $key;
            }
        }

        if (is_array($value)) {
            return $key;
        }

        if (!is_string($value) || empty($replace)) {
            return $value;
        }

        $placeholders = [];
        $replacements = [];

        foreach ($replace as $key => $data) {
            $placeholders[] = '{{' . $key . '}}';
            $replacements[] = (string) $data;
        }

        return str_replace($placeholders, $replacements, $value);
    }
}

if (! function_exists('lt')) {
    /**
     * Returns a "lazy" translation closure for use in service providers.
     * 't' stands for 'lazy translate'.
     *
     * @param string $key The translation key.
     * @param array $replace Placeholder replacements.
     * @return \Closure
     */
    function lt(string $key, array $replace = [], $locale = null)
    {
        return fn (): string => t($key, $replace, $locale);
    }
}

if (!function_exists('perform_placeholder_replacement')) {
    /**
     * Replaces placeholders in a string.
     * Supports both :placeholder and {{placeholder}} syntax.
     *
     * @param string $translation The string with placeholders.
     * @param array $replace The associative array of replacements.
     * @return string The translated string with placeholders replaced.
     */
    function perform_placeholder_replacement($translation, array $replace = [])
    {
        if (empty($replace)) {
            return $translation;
        }

        dd($replace);

        $placeholders = [];
        $replacements = [];

        foreach ($replace as $key => $value) {
            $placeholders[] = '{{' . $key . '}}';
            $replacements[] = (string) $value;
        }

        return str_replace($placeholders, $replacements, $translation);
    }
}

if(!function_exists('parseTemplate')) {
    function parseTemplate($template, $data, $replaceNotFoundData = true) {
        if (is_array($template)) {
            foreach ($template as $key => $value) {
                $template[$key] = parseTemplate($value, $data);
            }
            return $template;
        }

        return preg_replace_callback('/(\{\{(.+?)\}\})|(\$\{(.+?)\})|(\{(.+?)\})/', function ($matches) use ($data, $replaceNotFoundData) {
            $match = $matches[0];

            if (strpos($match, '${') === 0) {
                $match = substr($match, 2, -1);
            } elseif (strpos($match, '{{') === 0) {
                $match = substr($match, 2, -2);
            } elseif (strpos($match, '{') === 0) {
                $match = substr($match, 1, -1);
            }

            $keys = explode('.', $match);
            $value = $data;

            foreach ($keys as $key) {
                if (is_array($value) && array_key_exists($key, $value)) {
                    $value = $value[$key];
                } else {
                    return $replaceNotFoundData ? '-' : $matches[0];
                }
            }

            return $value !== null ? $value : ($replaceNotFoundData ? '-' : $matches[0]);
        }, $template);
    }
}

if (!function_exists('getDomainParts')) {
    /**
     * Extracts the full host, domain, and subdomain from a URL.
     *
     * @param string|null $url The URL string. Defaults to current request URL.
     * @return array Returns an associative array with 'host', 'domain', 'subdomain'.
     */
    function getDomainParts(?string $url = null): array
    {
        $url = $url ?? request()->getSchemeAndHttpHost();

        $parsedUrl = parse_url($url);

        $host = $parsedUrl['host'] ?? null;
        if (!$host) {
            return ['host' => null, 'domain' => null, 'subdomain' => null];
        }

        $hostWithoutPort = strtok($host, ':');

        $parts = explode('.', $hostWithoutPort);
        $numParts = count($parts);

        $domain = null;
        $subdomain = null;

        if ($numParts >= 2) {
            $domain = $parts[$numParts - 2] . '.' . $parts[$numParts - 1];

            if ($numParts >= 3 && in_array($parts[$numParts - 2], ['co', 'com', 'net', 'org']) && strlen($parts[$numParts - 1]) <= 3) {
                 $domain = $parts[$numParts - 3] . '.' . $parts[$numParts - 2] . '.' . $parts[$numParts - 1];
            }


            if ($hostWithoutPort === $domain) {
                $subdomain = null;
            } else {
                $subdomain = str_replace("." . $domain, "", $hostWithoutPort);
                if ($subdomain === 'www') {
                    $subdomain = null;
                }
            }
        } else if ($numParts == 1) {
            $domain = $hostWithoutPort;
            $subdomain = null;
        }


        return [
            'host' => $hostWithoutPort,
            'domain' => $domain,
            'subdomain' => $subdomain
        ];
    }
}


if(!function_exists('removeTrailingSlash')) {
    function removeTrailingSlash($path): string {
        if (!is_string($path) || $path == '' || $path == '/') {
            return (string) $path;
        } else {
            return rtrim($path, '/');
        }
    }
}

if(!function_exists('getClientIpAddress')) {
    function getClientIpAddress(){
        $clientIp = "";
        if (isset($_SERVER['HTTP_X_REAL_IP'])) {
            $clientIp = $_SERVER['HTTP_X_REAL_IP'];
        }
        elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $clientIp = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR'])[0];
        }
        else {
            $clientIp = $_SERVER['REMOTE_ADDR'];
        }
        return $clientIp;
    }
}

if(!function_exists('generateTestimonyHTML')) {
    function generateTestimonyHTML($testimonyData = []){
        $html = '';
        if(
            !$testimonyData
            || empty($testimonyData)
            || (!is_array($testimonyData) && $testimonyData->isEmpty())
        ) {
            return '<p>'. t('core.testimony.no_data') .'</p>';
        }
        foreach ($testimonyData as $item) {
            $countryLabel = $item->country?->name;
            $countryFlag = strtolower($item->country?->code_alpha_2);
            $html .= '
            <div class="testimonial mt-5" lang="' . htmlspecialchars($item['language_iso_2']) . '">
                <p>
                    <strong>
                        <span class="flag-icon flag-icon-' . htmlspecialchars($countryFlag) . '"></span>
                        <span class="label-lang">' . htmlspecialchars($countryLabel) . '</span>
                    </strong>
                </p>
                <p>
                    <strong>
                        ' . htmlspecialchars($item['name']) . '
                    </strong>
                </p>
                <p class="italic">“' . htmlspecialchars($item['testimony']) . '”</p>
            </div>';
        }
        return $html;
    }
}

if(!function_exists('getTranslatedUrlForIso')) {
    function getTranslatedUrlForIso($pathTranslated, $isoCode)
    {
        foreach ($pathTranslated as $entry) {
            if (
                isset($entry['language']['languageISO2']) &&
                $entry['language']['languageISO2'] === $isoCode
            ) {
                return $entry['path'];
            }
        }
        return null;
    }
}
