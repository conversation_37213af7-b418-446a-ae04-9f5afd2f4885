<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index(
        Request $request,
        SubscriptionService $subscriptionService
    ) {
        $user = auth()->user();
        $styles = ['resources/sass/components/dashboard.scss'];

        $totalProjects = Project::count();

        $projectsThisMonth = Project::whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->count();


        $subscriptionData = $subscriptionService->findCurrentActiveUserSubscription($user->id);
        $subscription = null;
        if ($subscriptionData) {
            $subscription = [
                'plan' => $subscriptionData->plan_json['name'],
                'users' => $subscriptionData->plan_json['user_count'],
                'since' => $subscriptionData['starts_at'],
                'projectsTotal' => $totalProjects,
                'credits' => $subscriptionData->plan_json['credit'],
                'periode' => $subscriptionData->plan_json['period'],
                'projectsMonth' => $projectsThisMonth,
                'balanceMonth' => $subscriptionData['credit_balance'],
            ];
        }


        return view('dashboard.index', [
            'styles' => $styles,
            'subscription' => $subscription,
            'user' => $user,
        ]);
    }

    public function createProject(Request $request)
    {
        $subscription = [
            'plan' => 'EXPERT',
            'users' => 3,
            'since' => '22/01/2025',
            'projectsTotal' => 140,
            'creditsYear' => 600,
            'creditsMonth' => 50,
            'projectsMonth' => 2,
            'balanceMonth' => 48,
        ];

        $styles = ['resources/sass/components/project-info.scss'];

        return view('dashboard.project-info', [
            'isInfo' => 'false',
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function createSimulation(Request $request)
    {
        $subscription = [
            'plan' => 'EXPERT',
            'users' => 3,
            'since' => '22/01/2025',
            'projectsTotal' => 140,
            'creditsYear' => 600,
            'creditsMonth' => 50,
            'projectsMonth' => 2,
            'balanceMonth' => 48,
        ];

        $styles = ['resources/sass/components/simulation.scss'];

        return view('dashboard.simulation', [
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function projectInfo(Request $request, $id)
    {
        $project = Project::findOrFail($id);

        $subscription = [
            'plan' => 'EXPERT',
            'users' => 3,
            'since' => '22/01/2025',
            'projectsTotal' => 140,
            'creditsYear' => 600,
            'creditsMonth' => 50,
            'projectsMonth' => 2,
            'balanceMonth' => 48,
        ];

        $styles = ['resources/sass/components/project-info.scss'];

        return view('dashboard.project-info', [
            'project' => $project,
            'isInfo' => 'true',
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

}
