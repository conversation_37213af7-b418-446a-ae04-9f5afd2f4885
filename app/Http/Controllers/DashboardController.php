<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    private SubscriptionService $subscriptionService;
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }
    private function getSubscription() {
        $user = auth()->user();
        $subscriptionData = $this->subscriptionService->findCurrentActiveUserSubscription($user->id);
        $subscription = null;
        if ($subscriptionData) {

            $totalProjects = Project::where('created_by', $user->id)->count();

            $projectsThisMonth = Project::where('created_by', $user->id)
                ->whereYear('created_at', now()->year)
                ->whereMonth('created_at', now()->month)
                ->count();
            $subscription = [
                'plan' => $subscriptionData->plan_json['name'],
                'users' => $subscriptionData->plan_json['user_count'],
                'since' => $subscriptionData['starts_at'],
                'projectsTotal' => $totalProjects,
                'credits' => $subscriptionData->plan_json['credit'],
                'periode' => $subscriptionData->plan_json['period'],
                'projectsMonth' => $projectsThisMonth,
                'balanceMonth' => $subscriptionData['credit_balance'],
            ];
        }
        return $subscription;
    }
    public function index(
        Request $request
    ) {
        $user = auth()->user();
        $styles = ['resources/sass/components/dashboard.scss'];

        $totalProjects = Project::where('created_by', $user->id)->count();

        $projectsThisMonth = Project::where('created_by', $user->id)
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->count();


        $subscription = $this->getSubscription();


        return view('dashboard.index', [
            'styles' => $styles,
            'subscription' => $subscription,
            'user' => $user,
        ]);
    }

    public function createProject(
        Request $request
    ) {

        $user = auth()->user();
        $subscription = $this->getSubscription();

        $styles = ['resources/sass/components/project-info.scss'];

        return view('dashboard.project-info', [
            'isInfo' => 'false',
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function createSimulation(Request $request)
    {
        $subscription = $this->getSubscription();;

        $styles = ['resources/sass/components/simulation.scss'];

        return view('dashboard.simulation', [
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function pdfSimulation(Request $request)
    {
        $subscription = $this->getSubscription();;

        $styles = ['resources/sass/components/simulation.scss', 'resources/sass/components/pdf_editor.scss'];

        return view('dashboard.pdf-simulation', [
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function projectInfo(Request $request, $id)
    {
        $project = Project::findOrFail($id);

        $subscription = $this->getSubscription();;

        $styles = ['resources/sass/components/project-info.scss'];

        return view('dashboard.project-info', [
            'project' => $project,
            'isInfo' => 'true',
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

}
