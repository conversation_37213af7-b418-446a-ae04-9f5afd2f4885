<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\ExampleNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

use function PHPSTORM_META\type;

class NotificationController extends Controller
{
    public function send(Request $request)
    {
        
        $data = $request->validate([
            'type'    => 'required|string|max:500',
        ]);

        $userToNotify = User::find(1);

        if (!$userToNotify) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }
        // Then notify this single user
        Notification::send($userToNotify, new ExampleNotification($userToNotify, $data['type'], "This is a notification form the api"));
        
        return response()->json([
            'success' => true,
            'sent_to' => $userToNotify->pluck('id'),
            "type" => $data['type']
        ], 200);
    }
}
