<?php
namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\Request;
use App\Http\Resources\ProjectResource;
use Illuminate\Support\Facades\Log;

class ProjectController extends Controller
{
    public function index(Request $request)
    {
        if (!auth()->check()) {
            abort(401, 'Unauthenticated.');
        }

        $user = auth()->user();
        $userId = auth()->id();
        $perPage = $request->input('per_page', 10);
        $orderBy = $request->input('order_by', 'updated_at');
        $order = $request->input('order', 'desc');
        $itemsLimit = (int) $request->input('items_limit', 10);

        $allowedOrderBy = ['id', 'project_name', 'created_at', 'updated_at', 'project_type', 'city'];
        $allowedOrder = ['asc', 'desc'];

        if (!in_array($orderBy, $allowedOrderBy)) {
            $orderBy = 'updated_at';
        }

        if (!in_array(strtolower($order), $allowedOrder)) {
            $order = 'desc';
        }

        // Only load items if explicitly requested in columns
        $columns = explode(',', $request->input('columns', ''));
        $loadItems = in_array('items', $columns);

        $query = Project::query();

        $subscriptionIds = $user->subscriptions()->pluck('id');
        $query->where('created_by', $userId);
        // ->orWhereIn('subscription_id', $subscriptionIds);

        if ($request->filled('project_name')) {
            $query->where('project_name', 'like', '%' . $request->project_name . '%');
        }
        if ($request->filled('project_type')) {
            $query->where('project_type', $request->project_type);
        }
        if ($request->filled('location_type')) {
            $query->where('location_type', $request->location_type);
        }
        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->city . '%');
        }
        if ($request->filled('id')) {
            $query->where('id', 'like', $request->id);
        }
        // Date exacte
        if ($request->filled('created_at')) {
            $query->whereDate('created_at', $request->created_at);
        }

        if ($request->filled('updated_at')) {
            $query->whereDate('updated_at', $request->updated_at);
        }

        /* Date Range if needed
            if ($request->filled('created_from')) {
                $query->whereDate('created_at', '>=', $request->created_from);
            }

            if ($request->filled('created_to')) {
                $query->whereDate('created_at', '<=', $request->created_to);
            }

            if ($request->filled('updated_from')) {
                $query->whereDate('updated_at', '>=', $request->updated_from);
            }

            if ($request->filled('updated_to')) {
                $query->whereDate('updated_at', '<=', $request->updated_to);
            }
        */


        if ($loadItems) {
            $query->with([
                'items' => function ($q) use ($itemsLimit, $request) {
                    $q->select('id', 'item_name', 'item_type', 'project_id', 'updated_at', 'created_at')
                        ->when($request->filled('project_item_type'), function ($query) use ($request) {
                            $query->where('item_type', $request->project_item_type);
                        })
                        ->orderByDesc('updated_at')
                        ->limit($itemsLimit);
                }
            ]);
        }

        $projects = $query->orderBy($orderBy, $order)
            ->paginate($perPage)
            ->appends($request->query());

        return ProjectResource::collection($projects);
    }
    public function getProjectTypes(Request $request)
    {
        $types = Project::query()
            ->select('project_type')
            ->whereNotNull('project_type')
            ->distinct()
            ->orderBy('project_type', 'asc')
            ->pluck('project_type');

        return response()->json([
            'data' => $types
        ]);
    }
    public function store(Request $request)
    {
        if (!auth()->check()) {
            abort(401, 'Unauthenticated.');
        }
        $user = auth()->user();
        $data = $request->validate([
            'project_name' => 'required|string|max:255',
            'project_info' => 'nullable|string',
            'project_type' => 'nullable|string',
            'location_type' => 'nullable|string',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'street_number' => 'nullable|string',
            'street' => 'nullable|string',
            'city' => 'nullable|string',
            'country' => 'nullable|string',
            'country_id' => 'nullable|exists:countries,id',
            'subscription_id' => 'nullable|exists:subscriptions,id',
        ]);
        //Handle creditBalance no eto
        // $subscription = Subscription::find($data['subscription_id']);
        // if (!$subscription || $subscription->credit_balance <= 0) {
        //     throw ValidationException::withMessages([
        //         'subscription_id' => ['Subscription is invalid or has no available credits.']
        //     ]);
        // }

        $data['created_by'] = $user->id;
        $data['last_user_to_interact'] = $user->id;
        $project = Project::create($data);


        // $subscription->decrement('credit_balance');
        return new ProjectResource($project->load('items'));
    }

    public function show(Request $request, Project $project)
    {
        if (!auth()->check()) {
            abort(401, 'Unauthenticated.');
        }
        $itemsLimit = (int) $request->input('items_limit', 10);

        $project->load([
            'items' => function ($query) use ($itemsLimit) {
                $query->limit($itemsLimit);
            }
        ]);

        return new ProjectResource($project);
    }

    public function update(Request $request, Project $project)
    {
        if (!auth()->check()) {
            abort(401, 'Unauthenticated.');
        }
        $user = auth()->user();
        $data = $request->validate([
            'project_name' => 'sometimes|required|string|max:255',
            'project_info' => 'nullable|string',
            'project_type' => 'nullable|string',
            'location_type' => 'nullable|string',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'street_number' => 'nullable|string',
            'street' => 'nullable|string',
            'city' => 'nullable|string',
            'country_id' => 'nullable|exists:countries,id',
            'subscription_id' => 'nullable|exists:subscriptions,id',
        ]);

        $data['last_user_to_interact'] = $user->id;

        $project->update($data);
        return new ProjectResource($project->fresh('items'));
    }

    public function destroy(Project $project)
    {
        if (!auth()->check()) {
            abort(401, 'Unauthenticated.');
        }
        $project->delete();
        return response()->json(['message' => 'Project deleted']);
    }
}
