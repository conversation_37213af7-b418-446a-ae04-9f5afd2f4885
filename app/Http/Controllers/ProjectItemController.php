<?php
namespace App\Http\Controllers;

use App\Models\ProjectItem;
use Illuminate\Http\Request;
use App\Http\Resources\ProjectItemResource;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;

class ProjectItemController extends Controller
{
    public function index()
    {
        return ProjectItemResource::collection(ProjectItem::with('project')->paginate(20));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'item_name' => 'required|string|max:255',
            'item_info' => 'nullable|array',
            'item_type' => 'nullable|string|max:255',
            'created_by' => 'nullable|exists:users,id',
            'last_user_to_interact' => 'nullable|exists:users,id',
        ]);

        $item = ProjectItem::create($data);
        return new ProjectItemResource($item->load('project'));
    }

    public function show(ProjectItem $projectItem)
    {
        return new ProjectItemResource($projectItem->load('project'));
    }

    public function update(Request $request, ProjectItem $projectItem)
    {
        $data = $request->validate([
            'item_name' => 'sometimes|required|string|max:255',
            'item_info' => 'nullable|array',
            'item_type' => 'nullable|string|max:255',
            'created_by' => 'nullable|exists:users,id',
            'last_user_to_interact' => 'nullable|exists:users,id',
        ]);

        $projectItem->update($data);
        return new ProjectItemResource($projectItem->fresh('project'));
    }

    public function destroy(ProjectItem $projectItem)
    {
        $projectItem->delete();
        return response()->json(['message' => 'Project item deleted']);
    }

    public function getProjectItemTypes(Request $request)
    {
        $types = ProjectItem::query()
            ->select('item_type')
            ->whereNotNull('item_type')
            ->distinct()
            ->orderBy('item_type', 'asc')
            ->pluck('item_type');

        return response()->json([
            'data' => $types
        ]);
    }

    /**
     * Generate a PDF from simulation variables
     */
    public function generatePdf(Request $request)
    {
        $data = $request->validate([
            'title' => 'required|string',
            'user' => 'required|string',
            'date' => 'required|string',
            'lang' => 'string',
        ]);

        $user = auth()->user();

        logger(config('services.gotenberg.url'));
        // Create Gotenberg client
        $client = Gotenberg::chromium(config('services.gotenberg.url'));

        // Build HTMLRequest
        $outputFilename = 'simulation_' . $user['id'];
        $request = $client->pdf()->outputFilename($outputFilename)->paperSize('29.7cm', '21cm')->margins(0, 0, 0, 0);

        // Prepare data for all pages
        $pages = [
            // 'pdf.simulation.under-construction' => $data,
            'pdf.simulation.header' => $data,
            'pdf.simulation.page2' => ['info' => 'Page 2 Example', 'title' => $data['title']],
            'pdf.simulation.page3' => ['info' => 'Page 3 Example', 'title' => $data['title']],
        ];
        $companyLogo = $user->getCompanyLogoUrl();
        $urlImage = $companyLogo ? config('app.url') . $companyLogo : null;

        $pageNumber = 1; // Start counting at 1

        $html = '';
        foreach ($pages as $blade => $pageData) {
            $html .= view($blade, array_merge([
                'lang' => $data['lang'] ?? 'en',
                'pageIdx' => $pageNumber,
                'urlImage' => $urlImage,
            ], $pageData))->render();
            $pageNumber++;
        }

        // $path = "{$tmpDir}/page.html";
        // file_put_contents($path, $html);
        $request = $request->html(Stream::string('index.html', $html));

        // Send and store or get PDF
        Gotenberg::save($request, storage_path('tmp'));

        return response()->download(storage_path('tmp/' . $outputFilename . '.pdf'))->deleteFileAfterSend(true);

        // $pdfOptions = [
        //     // 'defaultFont' => 'Roboto Condensed',
        //     'isHtml5ParserEnabled' => true,
        //     'isRemoteEnabled' => true,
        // ];

        // $pdf = \Barryvdh\DomPDF\Facade\Pdf::setOptions($pdfOptions)
        //     ->setPaper('a4', 'landscape')
        //     ->loadHTML($html);

        // $pdfContent = $pdf->output();

        // // Use a custom mime type to bypass extra-tools (idm, ...)
        // return response($pdfContent, 200, [
        //     'Content-Type' => 'application/x-simulation-pdf', // custom mime type
        //     'Content-Disposition' => 'attachment; filename="simulation.pdf"',
        //     'Cache-Control' => 'private, max-age=0, must-revalidate',
        //     'Pragma' => 'public',
        // ]);
    }

}
