<?php
namespace App\Http\Controllers;

use App\Models\ProjectItem;
use Illuminate\Http\Request;
use App\Http\Resources\ProjectItemResource;

class ProjectItemController extends Controller
{
    public function index()
    {
        return ProjectItemResource::collection(ProjectItem::with('project')->paginate(20));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'item_name' => 'required|string|max:255',
            'item_info' => 'nullable|array',
            'item_type' => 'nullable|string|max:255',
            'created_by' => 'nullable|exists:users,id',
            'last_user_to_interact' => 'nullable|exists:users,id',
        ]);

        $item = ProjectItem::create($data);
        return new ProjectItemResource($item->load('project'));
    }

    public function show(ProjectItem $projectItem)
    {
        return new ProjectItemResource($projectItem->load('project'));
    }

    public function update(Request $request, ProjectItem $projectItem)
    {
        $data = $request->validate([
            'item_name' => 'sometimes|required|string|max:255',
            'item_info' => 'nullable|array',
            'item_type' => 'nullable|string|max:255',
            'created_by' => 'nullable|exists:users,id',
            'last_user_to_interact' => 'nullable|exists:users,id',
        ]);

        $projectItem->update($data);
        return new ProjectItemResource($projectItem->fresh('project'));
    }

    public function destroy(ProjectItem $projectItem)
    {
        $projectItem->delete();
        return response()->json(['message' => 'Project item deleted']);
    }

    public function getProjectItemTypes(Request $request)
    {
        $types = ProjectItem::query()
            ->select('item_type')
            ->whereNotNull('item_type')
            ->distinct()
            ->orderBy('item_type', 'asc')
            ->pluck('item_type');

        return response()->json([
            'data' => $types
        ]);
    }
}
