<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\LoginService;
use App\Validator\LoginValidator;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    //    /**
    //     * Where to redirect users after login.
    //     *
    //     * @var string
    //     */
    //    protected $redirectTo = RouteServiceProvider::HOME;

    public function __construct(
        private LoginValidator $loginValidator,
        private LoginService $loginService,
    ) {
        $this->middleware('guest')->except('logout');
    }

    public function redirectPath()
    {
        return Redirect::getIntendedUrl() ?? route('home', ['lang' => app()->getLocale()]);
    }

    public function showLoginForm()
    {
        if (url()->previous() != route('register') && Redirect::getIntendedUrl() === null) {
            Redirect::setIntendedUrl(url()->previous()); // make sure we redirect back to the page we came from
        }

        return view('auth.login');
    }

    protected function authenticated(Request $request, $user)
    {
        if ($user->is_blocked) {
            $this->guard()->logout();

            return redirect()->route('login')->withErrors([
                'account' => 'Your account has been blocked. Please contact support.',
            ]);
        }

        if ($user->isDeleted()) {
            $this->guard()->logout();

            return redirect()->route('login')->withErrors([
                'account' => 'Your account has been deleted',
            ]);
        }

        if (!$user->hasVerifiedEmail()) {
            $this->guard()->logout();

            return redirect()->route('login')->withErrors([
                'account' => "La vérification de l'e-mail n'est pas terminée, veuillez vérifier votre e-mail pour continuer",
            ]);
        }

        if ($request->filled('redirect-step-2') && str_starts_with($request->input('redirect-step-2'), '/')) {
            return redirect()->to($request->input('redirect-step-2') . '?step=2');
        }
        
        if ($request->getHost() === config('services.app_main.url')) {
            $token = Str::random(40);
            Cache::put('login_token_' . $token, $user->id, now()->addMinutes(5));

            return redirect()->away(config('services.base_url.dashboard')."/login-with-token?token={$token}");
        }

        return redirect()->intended($this->redirectPath());
    }

    protected function validateLogin(Request $request)
    {
        $this->loginValidator->validateRequest($request);
    }

    protected function attemptLogin(Request $request)
    {
        return $this->loginService->attempt($this->credentials($request), $request->boolean('remember'));
    }

    public function logoutCustom(Request $request)
    {
        $currentHost = $request->getHost();
        $user = Auth::user();
        
        $logoutToken = Str::random(40);
        Cache::put('logout_token_' . $logoutToken, $user->id, now()->addMinutes(5));
        
        // Auth::logout();
        // $request->session()->invalidate();
        // $request->session()->regenerateToken();


        dd($currentHost);   

        if ($currentHost === config('services.app_main.url')) {
            return redirect()->away(config('services.base_url.admin') . "/logout-from-main?logout_token={$logoutToken}");
        }
        
        if ($currentHost === 'dashboard.localhost') {
            return redirect()->away(config('services.base_url.app') . "/logout-from-dashboard?logout_token={$logoutToken}");
        }
    }

    public function logoutFromDashboard(Request $request)
    {
        $logoutToken = $request->query('logout_token');
        
        if ($logoutToken) {
            $userId = Cache::pull('logout_token_' . $logoutToken);
            
            if ($userId) {
                // Forcer le logout si l'utilisateur est connecté avec le même ID
                if (Auth::check() && Auth::id() == $userId) {
                    Auth::logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                }
            }
        }
        
        // Logout de sécurité
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
          return response()->json(['success' => true]);
    }

    public function logoutFromMain(Request $request)
    {
        $logoutToken = $request->query('logout_token');
        
        if ($logoutToken) {
            $userId = Cache::pull('logout_token_' . $logoutToken);
            
            if ($userId) {
                // Forcer le logout si l'utilisateur est connecté avec le même ID
                if (Auth::check() && Auth::id() == $userId) {
                    Auth::logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                }
            }
        }
        
        // Logout de sécurité
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
          return response()->json(['success' => true]);
    }
}
