<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Cart extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_id',
        'plan_json',
        'step_id',
        'cart_uid',
        'country_id',
        'ip_address',
        'language_iso_2',
        'converted_at',
        'user_id',
    ];

    protected $casts = [
        'plan_json' => 'array',
        'converted_at' => 'datetime',
        'step_id' => 'integer',
    ];

    protected static function booted(): void
    {
        static::creating(function (Cart $cart) {
            if (empty($cart->cart_uid)) {
                $cart->cart_uid = Str::uuid()->toString();
            }
        });
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the cart has been converted (subscription purchased)
     */
    public function isConverted(): bool
    {
        return $this->converted_at !== null;
    }

    /**
     * Mark the cart as converted
     */
    public function markAsConverted(?int $userId = null): void
    {
        $this->update([
            'converted_at' => now(),
            'user_id' => $userId ?? $this->user_id,
        ]);
    }

    /**
     * Update the step if it's higher than current step
     */
    public function updateStep(int $step): void
    {
        if ($step > $this->step_id && $step >= 1 && $step <= 3) {
            $this->update(['step_id' => $step]);
        }
    }

    /**
     * Scope for unconverted carts
     */
    public function scopeUnconverted($query)
    {
        return $query->whereNull('converted_at');
    }

    /**
     * Scope for converted carts
     */
    public function scopeConverted($query)
    {
        return $query->whereNotNull('converted_at');
    }
}
