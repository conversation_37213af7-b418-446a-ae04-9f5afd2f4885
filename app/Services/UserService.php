<?php

namespace App\Services;

use App\Models\User;
use App\Models\AccountInformation;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function createUser(array $data): User
    {
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ]);

        $particularId = 1;
        AccountInformation::updateOrCreate(
            ['user_id' => $user->id],
            [
                'account_type_id' => $particularId,
            ]
        );

        return $user;
    }
}
