<?php

namespace App\Services\PaymentProviders\Stripe;

use App\Constants\DiscountConstants;
use App\Constants\PaymentProviderConstants;
use App\Constants\PaymentProviderPlanPriceType;
use App\Constants\PlanMeterConstants;
use App\Constants\PlanPriceTierConstants;
use App\Constants\PlanPriceType;
use App\Constants\PlanType;
use App\Constants\CheckoutActionTypeEnum;
use App\Constants\SubscriptionType;
use App\Filament\Dashboard\Resources\SubscriptionResource;
use App\Models\Discount;
use App\Models\OneTimeProduct;
use App\Models\Order;
use App\Models\PaymentProvider;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Services\CalculationService;
use App\Services\DiscountService;
use App\Services\OneTimeProductService;
use App\Services\PaymentProviders\PaymentProviderInterface;
use App\Services\PlanService;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;

class StripeProvider implements PaymentProviderInterface
{
    private $stripeSupportedLanguage;
    private $language;
    private $stripeLanguage;

    public function __construct(
        private SubscriptionService $subscriptionService,
        private CalculationService $calculationService,
        private PlanService $planService,
        private DiscountService $discountService,
        private OneTimeProductService $oneTimeProductService,
    ) {
        $this->stripeSupportedLanguage = ["auto", "bg", "cs", "da", "de", "el", "en", "en-GB", "es", "es-419", "et", "fi", "fil", "fr", "fr-CA", "hr", "hu", "id", "it", "ja", "ko", "lt", "lv", "ms", "mt", "nb", "nl", "pl", "pt", "pt-BR", "ro", "ru", "sk", "sl", "sv", "th", "tr", "vi", "zh", "zh-HK", "zh-TW"];
        $this->language = 'en'; //TODO: set the language
        $this->stripeLanguage = in_array($this->language, $this->stripeSupportedLanguage) ? $this->language : "auto";

    }
    public function getSetupIntentClientSecret (User $user){
        $stripe = $this->getClient();
        $setupIntent = $stripe->setupIntents->create([
            'customer' => $this->findOrCreateStripeCustomer($user),
            'usage' => 'off_session',
        ]);

        return $setupIntent->client_secret;
    }
    public function createSubscriptionCheckoutRedirectLink(Plan $plan, Subscription $subscription, ?Discount $discount = null): string
    {
        $paymentProvider = $this->assertProviderIsActive();

        /** @var User $user */
        $user = auth()->user();

        try {

            $stripeCustomerId = $this->findOrCreateStripeCustomer($user);
            $stripeProductId = $this->findOrCreateStripeSubscriptionProduct($plan, $paymentProvider);
            $stripePrices = $this->findOrCreateStripeSubscriptionProductPrices($plan, $paymentProvider, $stripeProductId);

            $lineItems = $this->buildLineItems($stripePrices, $plan);

            $stripe = $this->getClient();

            $trialDays = 0;
            if ($plan->has_trial) {
                $trialDays = $this->subscriptionService->calculateSubscriptionTrialDays($plan);
            }

            $currencyCode = $subscription->currency()->firstOrFail()->code;

            $sessionCreationObject = [
                'customer' => $stripeCustomerId,
                'success_url' => $this->getSubscriptionCheckoutSuccessUrl($subscription),
                'cancel_url' => $this->getSubscriptionCheckoutCancelUrl($plan, $subscription),
                'mode' => 'subscription',
                'line_items' => $lineItems,
                'saved_payment_method_options' =>['payment_method_save' => 'enabled'],
                'subscription_data' => [
                    'metadata' => [
                        'subscription_uuid' => $subscription->uuid,
                    ],
                ],
            ];

            $shouldSkipTrial = $this->subscriptionService->shouldSkipTrial($subscription);

            if (! $shouldSkipTrial && $trialDays > 0) {
                $sessionCreationObject['subscription_data']['trial_period_days'] = $trialDays;
            }

            if ($discount !== null) {
                $stripeDiscountId = $this->findOrCreateStripeDiscount($discount, $paymentProvider, $currencyCode);

                $sessionCreationObject['discounts'] = [
                    [
                        'coupon' => $stripeDiscountId,
                    ],
                ];
            }

            $session = $stripe->checkout->sessions->create($sessionCreationObject);

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            throw $e;
        }

        return $session->url;
    }

    public function initProductCheckout(Order $order, ?Discount $discount = null): array
    {
        // stripe does not need any initialization

        return [];
    }

    public function createProductCheckoutRedirectLink(Order $order, ?Discount $discount = null): string
    {
        $paymentProvider = $this->assertProviderIsActive();

        try {
            $stripe = $this->getClient();

            $stripeCustomerId = $this->findOrCreateStripeCustomer($order->user);

            $sessionCreationObject = [
                'customer' => $stripeCustomerId,
                'success_url' => route('checkout.product.success'),
                'cancel_url' => route('checkout.product'),
                'mode' => 'payment',
                'line_items' => [],
                'payment_intent_data' => [
                    'metadata' => [
                        'order_uuid' => $order->uuid,
                    ],
                ],
            ];

            foreach ($order->items()->get() as $item) {
                $product = $item->oneTimeProduct()->firstOrFail();
                $stripeProductId = $this->findOrCreateStripeOneTimeProduct($product, $paymentProvider);
                $stripePriceId = $this->findOrCreateStripeOneTimeProductPrice($product, $paymentProvider, $stripeProductId);

                $sessionCreationObject['line_items'][] = [
                    'price' => $stripePriceId,
                    'quantity' => $item->quantity,
                ];
            }

            if ($discount !== null) {  // rethink about that when adding support for cart checkout (multiple products checkout) as this discount will be applied to the whole cart (to all products)
                $stripeDiscountId = $this->findOrCreateStripeDiscount($discount, $paymentProvider, $order->currency()->firstOrFail()->code);

                $sessionCreationObject['discounts'] = [
                    [
                        'coupon' => $stripeDiscountId,
                    ],
                ];
            }

            $session = $stripe->checkout->sessions->create($sessionCreationObject);

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            throw $e;
        }

        return $session->url;
    }



    public function changePlan(
        array $params
    ) {

        $oldSubscription = $params ['subscription'];
        $newPlan = $params ['newPlan'];
        $paymentProcessParams = $params ['paymentProcessParams'] ?? [];
        $accounting = $params ['accounting'] ?? [];
        $isProrated = $paymentProcessParams['isProrated'] ?? false;
        $isDeferred = $paymentProcessParams['isDeferred'] ?? false;
        $isRefunded = $paymentProcessParams['isRefunded'] ?? false;

        $newSubscriptionId = $params['newSubscriptionId'] ?? null;

        $paymentProvider = $this->assertProviderIsActive();

        try {

            $stripeProductId = $this->findOrCreateStripeSubscriptionProduct($newPlan, $paymentProvider);
            $stripePrices = $this->findOrCreateStripeSubscriptionProductPrices($newPlan, $paymentProvider, $stripeProductId);
            $newPlanLineItems = $this->buildLineItems($stripePrices, $newPlan);
            $stripe = $this->getClient();

            $newPlanPrice = $this->calculationService->getPlanPrice($newPlan);
            $params = [
                        'stripe' => $stripe,
                        "oldSubscription" => $oldSubscription,
                        "newPlanLineItems" =>  $newPlanLineItems,
                        "isProrated" => $isProrated,
                        "newPlan" =>  $newPlan,
                        "newPlanPrice" => $newPlanPrice,
                        "newSubscriptionId" => $newSubscriptionId
            ];

            /** @var Subscription nextSubscription */
            $nextSubscription = null;
            if(!$isDeferred){
                $nextSubscription = $this->directChangePlan(
                     $params
                );
            }else {
                $nextSubscription = $this->deferChangePlan(
                   $params
                );
            }

            if($isRefunded && isset($accounting["total"]) && $accounting["total"] < 0) {
                $stripeData = $oldSubscription->user->stripeData()->firstOrFail();
                $stripeCustomerId = $stripeData->stripe_customer_id;
                $this->executeRefund($stripeCustomerId, -$accounting["total"]);
            }


        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            throw $e;
        }

        return $nextSubscription;
    }
    public function directChangePlan(
        $params
        ){
        [
            'stripe' => $stripe,
            "oldSubscription" => $oldSubscription,
            "newPlanLineItems" =>  $newPlanLineItems,
            "newPlan" =>  $newPlan,
            "newPlanPrice" => $newPlanPrice
        ]= $params;

        $subscriptionItems = $stripe->subscriptionItems->all([
            'subscription' => $oldSubscription->payment_provider_subscription_id,
        ]);

        // remove old items from subscription and add new ones
        $itemsToDelete = [];
        foreach ($subscriptionItems as $subscriptionItem) {
            $itemsToDelete[] = [
                'id' => $subscriptionItem->id,
                'deleted' => true,
            ];
        }

        $subscriptionUpdateObject = [
            'items' => array_merge($itemsToDelete, $newPlanLineItems),
            'proration_behavior' => 'none'

        ];



        $stripe->subscriptions->update($oldSubscription->payment_provider_subscription_id, $subscriptionUpdateObject);

        $completeNewPlan =   $this->planService->getCompletePlanById($newPlan->id);
        $usedCredit =  $oldSubscription->plan_json['product']['credit_count'] - $oldSubscription->credit_balance;
        $newCreditBalance = $completeNewPlan->product->credit_count - $usedCredit;

        $this->subscriptionService->updateSubscription($oldSubscription, [
            'plan_id' => $newPlan->id,
            'price' => $newPlanPrice->price,
            'currency_id' => $newPlanPrice->currency_id,
            'interval_id' => $newPlan->interval_id,
            'interval_count' => $newPlan->interval_count,
            'credit_balance' => $newCreditBalance,
            'plan_json' => $completeNewPlan->toArray(),
        ]);
        return Subscription::find($oldSubscription->id);
    }

    public function deferChangePlan(
        $params
        ){
        [
            'stripe' => $stripe,
            "oldSubscription" => $oldSubscription,
            "newPlanLineItems" =>  $newPlanLineItems,
            "newPlan" =>  $newPlan,
            "newPlanPrice" => $newPlanPrice,
            "newSubscriptionId" => $newSubscriptionId
        ] = $params;


        $stripeData = $oldSubscription->user->stripeData()->firstOrFail();
        $stripeCustomerId = $stripeData->stripe_customer_id;
        $oldStripeSubscriptionId = $oldSubscription->payment_provider_subscription_id;
        $newStripePriceId = $newPlanLineItems[0]['price']; // assuming one line item
        $newStripePrice = $stripe->prices->retrieve($newStripePriceId, []);
        $oldStripeSubscription = $stripe->subscriptions->retrieve($oldStripeSubscriptionId, []);
        $currentOldPeriodEndTmstmp = $oldStripeSubscription->items?->data[0]?->current_period_end;

        $firstNewPeriodEnd =  Carbon::createFromTimestampUTC($currentOldPeriodEndTmstmp)
            ->add($newPlan->interval->date_identifier, $newPlan->interval_count)
            ->timestamp;

        // This creates a payment, but it's only used for the flow without the embedded checkout

        // $stripe->invoiceItems->create([
        //     'customer' => $stripeCustomerId,
        //     'amount' => $newStripePrice->unit_amount,
        //     'currency' =>  $newStripePrice->currency,
        // ]);
        // $invoice = $stripe->invoices->create([
        //     'customer' => $stripeCustomerId,
        //     'auto_advance' => true, // Will auto-finalize and attempt payment
        // ]);


        $subscriptionCreationParams = [
            'customer' => $stripeCustomerId,
            'items' => [
                        [
                            'price' => $newStripePriceId,
                        ],
            ],
            'trial_end' => $firstNewPeriodEnd,
            'proration_behavior' => 'none',
        ];
        $createdSubscription = $stripe->subscriptions->create($subscriptionCreationParams );
        $stripe->subscriptions->update($oldStripeSubscriptionId, [
            'cancel_at_period_end' => true,
        ]);

        $oldSubscription->update(['is_canceled_at_end_of_cycle' => true, 'cancellation_reason' => 'Deferred update']);

        $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)
            ->where('is_active', 1)
            ->first();
        $endsAtTmstp = $this->getSubscriptionEndsAt($createdSubscription);
        $endsAt = Carbon::createFromTimestampUTC($endsAtTmstp)->toDateTimeString();
        $startsAt = Carbon::createFromTimestampUTC($firstNewPeriodEnd)->toDateTimeString();

        $newSubscription = $this->subscriptionService->activateNew(
            [
                'newSubscriptionId' => $newSubscriptionId,
                'startsAt' => $startsAt,
                'endsAt' => $endsAt,
                'planSlug' => $newPlan->slug,
                'userId' => $oldSubscription->user_id,
                'paymentProvider' => $paymentProvider,
                'paymentProviderSubscriptionId' => $createdSubscription->id,
            ]
        );


        return $newSubscription;
    }
    public function getSubscriptionByStripeId(string $stripeSubscriptionId)
    {
        $stripe = $this->getClient();
        return $stripe->subscriptions->retrieve($stripeSubscriptionId, []);
    }
    private function getSubscriptionEndsAt($subscriptionObject): ?string
    {
        if ($subscriptionObject->current_period_end !== null) {
            return $subscriptionObject->current_period_end;
        }

        if ($subscriptionObject->items !== null) { // change introduced in the Stripe API 2025-03-01.dashboard
            return $subscriptionObject->items?->data[0]?->current_period_end;
        }

        return null;
    }
    private function getPaymentProviderIdFromPlan (Plan $plan): string
    {
        // ONLY STRIPE
        $paymentProviderData = $plan->paymentProviderData()->firstOrFail();
        $stripeProductId = $paymentProviderData->payment_provider_product_id;
        $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)->firstOrFail();
        $stripePrices = $this->findOrCreateStripeSubscriptionProductPrices($plan, $paymentProvider , $stripeProductId);
        // dd($paymentProvider);
        return $stripePrices[PaymentProviderPlanPriceType::MAIN_PRICE->value];
    }

    private function getCheckoutActionType(Plan $newPlan, ?Subscription $prevSub = null): CheckoutActionTypeEnum
    {

        $newPrice = $this->calculationService->getPlanPrice($newPlan);
        $newPriceAmount = $newPrice->price;
        $prevPriceAmount = null;

        if ($prevSub !== null) {
            $prevPrice = $this->calculationService->getPlanPriceFromArray($prevSub->plan_json);
            $prevPriceAmount = $prevPrice['price'];
        }

        if ($prevSub === null) {
            return CheckoutActionTypeEnum::SUBSCRIPTION_CREATION;
        }

        if ($newPriceAmount === $prevPriceAmount) {
            return CheckoutActionTypeEnum::SUBSCRIPTION_SWITCH;
        }

        if ($newPriceAmount < $prevPriceAmount) {
            return CheckoutActionTypeEnum::SUBSCRIPTION_DOWNGRADE;
        }

        if ($newPriceAmount > $prevPriceAmount) {
            return CheckoutActionTypeEnum::SUBSCRIPTION_UPGRADE;
        }

        return CheckoutActionTypeEnum::ONE_TIME;
    }
    public function generateRefundCharges(
        string $stripeCustomerId,
        float $totalAmountToRefund,
        ?float $curRecursiveAmountToRefund = null,
        ?string $startingAfterChargeId = null){

        $stripe = $this->getClient();


        if (is_null($curRecursiveAmountToRefund)) {
            $curRecursiveAmountToRefund = $totalAmountToRefund;
        }



        $chargesToRefund = [];
        $chargesParams = [
            'customer' => $stripeCustomerId,
            'limit' => 100,
            'expand' => ['data.payment_intent.invoice'],
        ];
        if(  $startingAfterChargeId)
        {
            $chargesParams['starting_after'] = $startingAfterChargeId;
        }
        $chargesRes = $stripe->charges->all($chargesParams);

        foreach ($chargesRes->data as $charge) {
            $paymentIntent = $charge->payment_intent;
            $paymentIntentMetadata = $paymentIntent->metadata;

            // Check if charge is relevant
            // TODO: In php SDK 17.x.x, invoice is no more in the payment intent object. The invoice must be retrieved to check the following conditions
            // $invoice = $paymentIntent->invoice;
            // $isRelevantCharge = (
            //     // Is a subscription creation or renewal charge
            //     ($invoice &&
            //         (
            //             $invoice->billing_reason === 'subscription_cycle' ||
            //             $invoice->billing_reason === 'subscription_create'
            //         )
            //     ) ||
            //     // Is an update charge
            //     (
            //             isset($paymentIntentMetadata['checkoutActionType']) &&
            //             in_array(
            //                 $paymentIntentMetadata['checkoutActionType'],
            //                 [
            //                     CheckoutActionTypeEnum::SUBSCRIPTION_UPGRADE->value,
            //                     CheckoutActionTypeEnum::SUBSCRIPTION_DOWNGRADE->value,
            //                     CheckoutActionTypeEnum::SUBSCRIPTION_SWITCH->value,
            //                 ]
            //                 )
            //     )
            // );

            $isRelevantCharge = true;

            if (!$isRelevantCharge) {
                continue;
            }

            $curRefundableAmount = $charge->amount_captured - $charge->amount_refunded;

            if ($curRefundableAmount > 0) {
                $curAmountToRefund = min($curRefundableAmount, $curRecursiveAmountToRefund);
                $amountAlreadyRefunded = $totalAmountToRefund - $curRecursiveAmountToRefund;
                $curRecursiveAmountToRefund -= $curAmountToRefund;
                $amountToRefundLater = $curRecursiveAmountToRefund;

                $chargesToRefund[] = [
                    'charge' => $charge,
                    'amount_to_refund' => $curAmountToRefund ,
                    'amount_to_refund_later' => $amountToRefundLater ,
                    'amount_already_refunded' => $amountAlreadyRefunded ,
                ];
            }

            if ($curRecursiveAmountToRefund <= 0) {
                break;
            }
        }

        if ($chargesRes->has_more && $curRecursiveAmountToRefund > 0) {
            $lastChargeId = end($chargesRes->data)->id;
            $chargesToRefund = array_merge(
                $chargesToRefund,
                $this->generateRefundCharges($stripeCustomerId, $totalAmountToRefund, $curRecursiveAmountToRefund, $lastChargeId)
            );
        }

        if ($curRecursiveAmountToRefund > 0) {
            throw new Exception('Not enough charges to refund');
        }

        return $chargesToRefund;


    }
    public function executeRefund(string $stripeCustomerId, float $totalAmountToRefund){
        $chargesToRefund = $this->generateRefundCharges($stripeCustomerId, $totalAmountToRefund);
        $stripe = $this->getClient();
        foreach($chargesToRefund as $refundData) {
            $charge = $refundData['charge'];
            $amountToRefund = $refundData['amount_to_refund'];

            if ($amountToRefund > 0) {
                $stripe->refunds->create([
                    'charge' => $charge->id,
                    'amount' => $amountToRefund,
                ]);
            }
        }
    }
    public function generateEmbeddedSessionArgs(
        array $paymentProcessParams,
        User $user,
        Plan $newPlan,
        ?Subscription $prevSub = null,
        )
    {
        $sessionArgs = NULL;


        $returnUrl = url(config('app.dashboard_url')."/subscriptions");
        $stripeCustomerId = $this->findOrCreateStripeCustomer($user);
        $stripeNewPlanPriceId = $this->getPaymentProviderIdFromPlan($newPlan);
        // die();

        $subActionType = $this->getCheckoutActionType($newPlan, $prevSub);




        $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)->firstOrFail();

        $metadata = [
                'checkoutActionType' =>  $subActionType->value,
                'userId' => $user->id,
                'slug' => $newPlan->slug,
                'stripeCustomerId' => $stripeCustomerId,
                'planId' => $newPlan->id,
                'stripePlanId' => $stripeNewPlanPriceId,
                'prevSubId' => $prevSub ? $prevSub->id : null,
                'paymentProcessParamsJsonStr' => json_encode($paymentProcessParams),

        ];

        // New Subscription Creation
        if( $subActionType == CheckoutActionTypeEnum::SUBSCRIPTION_CREATION || $paymentProcessParams['isDeferred'] ) {
            $newSubscription = $this->subscriptionService->findOrCreateNew($newPlan->id, $user->id, $paymentProvider);
            $metadata['newSubscriptionId'] = $newSubscription->id;
        }
        if (
        $subActionType == CheckoutActionTypeEnum::SUBSCRIPTION_CREATION
        ) {

            $sessionArgs = [
                'locale' => $this->stripeLanguage,
                'customer' => $stripeCustomerId,
                'ui_mode' => 'embedded',
                'line_items' => [[

                    'price' =>$stripeNewPlanPriceId,
                    // 'price' => "price_1PrwixDHmU40Mj0nVFpVOTW3",
                    'quantity' => 1,

                ]],
                'mode' => 'subscription',
                'return_url' => $returnUrl,
                'subscription_data' => [
                    'metadata' =>  $metadata
                ],
                'metadata' => $metadata,
            ];


        } else if ($subActionType == CheckoutActionTypeEnum::ONE_TIME) {
            $additionalPrice = $this->calculationService->getPlanPrice($newPlan)->price;
            $sessionArgs = [
                'locale' => $this->stripeLanguage,
                'customer' => $stripeCustomerId,
                'ui_mode' => 'embedded',
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' =>  config('app.default_currency'),
                        'product_data' => [
                            'name' => 'One Time '. $newPlan->name,
                        ],
                        'unit_amount' => $additionalPrice ,
                    ],
                    'quantity' => 1,
                ]],

                'mode' => 'payment',  // Mode is 'payment' for one-time charges
                'return_url' => $returnUrl,
                'payment_intent_data' => [
                    'metadata' => $metadata
                ],

                'metadata' => $metadata,
            ];

        }else {
            // $accounting = $this->cartService->calculateTotal($subActionType, $curPaidSub, $cart, $isDiscountEligible, $discountData['discountValuePerc']);

            $accounting = $this->subscriptionService->calculTotal($paymentProcessParams, $newPlan, $prevSub->plan_json);

            $metadata["accountingJsonStr"] = json_encode($accounting);


            // WHY HERE ??
            $totalPrice = $accounting['total'];

            if ($totalPrice < 0) {
                $metadata['refundAmount'] = -$totalPrice;
                $totalPrice = 0;
            }


            $sessionArgs = [
                'locale' => $this->stripeLanguage,
                'customer' => $stripeCustomerId,
                'ui_mode' => 'embedded',
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' =>  config('app.default_currency'),
                        'product_data' => [
                            'name' => 'Subscription Upgrade/Downgrade to '. $newPlan->name,
                        ],
                        'unit_amount' => $totalPrice ,
                    ],
                    'quantity' => 1,
                ]],

                'mode' => 'payment',  // Mode is 'payment' for one-time charges
                'return_url' => $returnUrl,
                'payment_intent_data' => [
                    'metadata' => $metadata
                ],

                'metadata' => $metadata,
            ];

        }
        return $sessionArgs;
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        $paymentProvider = $this->assertProviderIsActive();

        try {
            $stripe = $this->getClient();

            $stripe->subscriptions->update($subscription->payment_provider_subscription_id, ['cancel_at_period_end' => true]);

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }

    public function discardSubscriptionCancellation(Subscription $subscription): bool
    {
        $paymentProvider = $this->assertProviderIsActive();

        try {
            $stripe = $this->getClient();

            $stripe->subscriptions->update($subscription->payment_provider_subscription_id, ['cancel_at_period_end' => false]);

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }

    public function getChangePaymentMethodLink(Subscription $subscription): string
    {
        $paymentProvider = $this->assertProviderIsActive();

        try {
            $stripe = $this->getClient();

            $portalConfigId = Cache::rememberForever('stripe.portal_configuration_id', function () use ($stripe) {
                $portal = $stripe->billingPortal->configurations->create([
                    'business_profile' => [
                        'headline' => __('Manage your subscription and payment details.'),
                    ],
                    'features' => [
                        'invoice_history' => ['enabled' => true],
                        'payment_method_update' => ['enabled' => true],
                        'customer_update' => ['enabled' => false],
                    ],
                ]);

                return $portal->id;
            });

            $portal = $stripe->billingPortal->sessions->create([
                'customer' => $subscription->user->stripeData()->firstOrFail()->stripe_customer_id,
                'return_url' => SubscriptionResource::getUrl(),
            ]);

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            return '/';
        }

        return $portal->url;
    }

    public function addDiscountToSubscription(Subscription $subscription, Discount $discount): bool
    {
        $paymentProvider = $this->assertProviderIsActive();

        try {
            $stripe = $this->getClient();

            $stripeDiscountId = $this->findOrCreateStripeDiscount($discount, $paymentProvider, $subscription->currency()->firstOrFail()->code);

            $stripe->subscriptions->update($subscription->payment_provider_subscription_id, [
                'coupon' => $stripeDiscountId,
            ]);

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }

    public function getSlug(): string
    {
        return PaymentProviderConstants::STRIPE_SLUG;
    }

    public function initSubscriptionCheckout(Plan $plan, Subscription $subscription, ?Discount $discount = null): array
    {
        // stripe does not need any initialization

        return [];
    }

    public function isRedirectProvider(): bool
    {
        return true;
    }

    public function isOverlayProvider(): bool
    {
        return false;
    }

    private function getClient(): StripeClient
    {
        return new StripeClient(config('services.stripe.secret_key'));
    }

    private function findOrCreateStripeSubscriptionProduct(Plan $plan, PaymentProvider $paymentProvider): string
    {
        $stripeProductId = $this->planService->getPaymentProviderProductId($plan, $paymentProvider);

        if ($stripeProductId !== null) {
            return $stripeProductId;
        }

        $stripe = $this->getClient();

        $stripeProductId = $stripe->products->create([
            'id' => $plan->slug.'-'.Str::random(),
            'name' => $plan->name,
            'description' => ! empty($plan->description) ? strip_tags($plan->description) : $plan->name,
        ])->id;

        $this->planService->addPaymentProviderProductId($plan, $paymentProvider, $stripeProductId);

        return $stripeProductId;
    }

    private function findOrCreateStripeOneTimeProduct(OneTimeProduct $product, PaymentProvider $paymentProvider): string
    {
        $stripeProductId = $this->oneTimeProductService->getPaymentProviderProductId($product, $paymentProvider);

        if ($stripeProductId !== null) {
            return $stripeProductId;
        }

        $stripe = $this->getClient();

        $stripeProductId = $stripe->products->create([
            'id' => $product->slug.'-'.Str::random(),
            'name' => $product->name,
            'description' => ! empty($product->description) ? strip_tags($product->description) : $product->name,
        ])->id;

        $this->oneTimeProductService->addPaymentProviderProductId($product, $paymentProvider, $stripeProductId);

        return $stripeProductId;
    }

    public function findOrCreateStripeCustomer(User $user): string
    {
        $stripe = $this->getClient();

        $stripeCustomerId = null;
        $stripeData = $user->stripeData();
        if ($stripeData->count() > 0) {
            $stripeData = $stripeData->first();
            $stripeCustomerId = $stripeData->stripe_customer_id;
        }

        if ($stripeCustomerId === null) {
            $customer = $stripe->customers->create(
                [
                    'email' => $user->email,
                    'name' => $user->name,
                ]
            );
            $stripeCustomerId = $customer->id;

            if ($stripeData->count() > 0) {
                $stripeData = $stripeData->first();
                $stripeData->stripe_customer_id = $stripeCustomerId;
                $stripeData->save();
            } else {
                $user->stripeData()->create([
                    'stripe_customer_id' => $stripeCustomerId,
                ]);
            }
        }

        return $stripeCustomerId;
    }

    private function findOrCreateStripeDiscount(Discount $discount, PaymentProvider $paymentProvider, string $currencyCode): string
    {
        $stripeDiscountId = $this->discountService->getPaymentProviderDiscountId($discount, $paymentProvider);

        if ($stripeDiscountId !== null) {
            return $stripeDiscountId;
        }

        $stripe = $this->getClient();

        $couponObject = [
            'name' => $discount->name,
        ];

        if ($discount->type == DiscountConstants::TYPE_FIXED) {
            $couponObject['amount_off'] = $discount->amount;
        } else {
            $couponObject['percent_off'] = $discount->amount;
        }

        $couponObject['currency'] = $currencyCode;

        if ($discount->duration_in_months !== null) {
            $couponObject['duration'] = 'repeating';
            $couponObject['duration_in_months'] = $discount->duration_in_months;
        } elseif ($discount->is_recurring) {
            $couponObject['duration'] = 'forever';
        } else {
            $couponObject['duration'] = 'once';
        }

        if ($discount->valid_until !== null) {
            $carbon = Carbon::parse($discount->valid_until);
            $couponObject['redeem_by'] = $carbon->timestamp;
        }

        $stripeCoupon = $stripe->coupons->create(
            $couponObject
        );

        $stripeDiscountId = $stripeCoupon->id;

        $this->discountService->addPaymentProviderDiscountId($discount, $paymentProvider, $stripeDiscountId);

        return $stripeDiscountId;
    }

    private function findOrCreateStripeSubscriptionProductPrices(Plan $plan, PaymentProvider $paymentProvider, string $stripeProductId): array
    {
        $planPrice = $this->calculationService->getPlanPrice($plan);

        $stripeProductPrices = $this->planService->getPaymentProviderPrices($planPrice, $paymentProvider);

        if (count($stripeProductPrices) > 0) {
            $result = [];
            foreach ($stripeProductPrices as $stripeProductPriceId) {
                $result[$stripeProductPriceId->type] = $stripeProductPriceId->payment_provider_price_id;
            }

            return $result;
        }

        $currencyCode = $planPrice->currency()->firstOrFail()->code;

        $stripe = $this->getClient();

        $results = [];

        if ($plan->type === PlanType::FLAT_RATE->value) {
            $stripeProductPriceId = $stripe->prices->create([
                'product' => $stripeProductId,
                'unit_amount' => $planPrice->price,
                'currency' => $planPrice->currency()->firstOrFail()->code,
                'recurring' => [
                    'interval' => $plan->interval()->firstOrFail()->date_identifier,
                    'interval_count' => $plan->interval_count,
                ],
            ])->id;

            $this->planService->addPaymentProviderPriceId($planPrice, $paymentProvider, $stripeProductPriceId, PaymentProviderPlanPriceType::MAIN_PRICE);

            $results[PaymentProviderPlanPriceType::MAIN_PRICE->value] = $stripeProductPriceId;

        } elseif ($plan->type === PlanType::USAGE_BASED->value) {

            $stripeMeterId = $this->findOrCreateStripeMeter($plan, $paymentProvider);

            if ($planPrice->price > 0) {  // fixed fee
                $stripeFixedFeeProductPriceId = $stripe->prices->create([
                    'product' => $stripeProductId,
                    'unit_amount' => $planPrice->price,
                    'currency' => $planPrice->currency()->firstOrFail()->code,
                    'billing_scheme' => 'per_unit',
                    'recurring' => [
                        'usage_type' => 'licensed',
                        'interval' => $plan->interval()->firstOrFail()->date_identifier,
                        'interval_count' => $plan->interval_count,
                    ],
                ])->id;

                $this->planService->addPaymentProviderPriceId($planPrice, $paymentProvider, $stripeFixedFeeProductPriceId, PaymentProviderPlanPriceType::USAGE_BASED_FIXED_FEE_PRICE);

                $results[PaymentProviderPlanPriceType::USAGE_BASED_FIXED_FEE_PRICE->value] = $stripeFixedFeeProductPriceId;
            }

            if ($planPrice->type === PlanPriceType::USAGE_BASED_PER_UNIT->value) {
                $stripeProductPriceId = $stripe->prices->create([
                    'product' => $stripeProductId,
                    'currency' => $currencyCode,
                    'unit_amount' => $planPrice->price_per_unit,
                    'billing_scheme' => 'per_unit',
                    'recurring' => [
                        'usage_type' => 'metered',
                        'interval' => $plan->interval()->firstOrFail()->date_identifier,
                        'interval_count' => $plan->interval_count,
                        'meter' => $stripeMeterId,
                    ],
                ])->id;

                $this->planService->addPaymentProviderPriceId($planPrice, $paymentProvider, $stripeProductPriceId, PaymentProviderPlanPriceType::USAGE_BASED_PRICE);

                $results[PaymentProviderPlanPriceType::USAGE_BASED_PRICE->value] = $stripeProductPriceId;

            } else {

                $tiersMode = 'graduated';
                if ($planPrice->type === PlanPriceType::USAGE_BASED_TIERED_VOLUME->value) {
                    $tiersMode = 'volume';
                }

                $tiers = [];
                foreach ($planPrice->tiers as $tier) {
                    $tiers[] = [
                        'up_to' => $tier[PlanPriceTierConstants::UNTIL_UNIT] === '∞' ? 'inf' : $tier[PlanPriceTierConstants::UNTIL_UNIT],
                        'unit_amount_decimal' => $tier[PlanPriceTierConstants::PER_UNIT],
                        'flat_amount_decimal' => $tier[PlanPriceTierConstants::FLAT_FEE],
                    ];
                }

                $tierPriceId = $stripe->prices->create([
                    'product' => $stripeProductId,
                    'currency' => $planPrice->currency()->firstOrFail()->code,
                    'billing_scheme' => 'tiered',
                    'recurring' => [
                        'usage_type' => 'metered',
                        'interval' => $plan->interval()->firstOrFail()->date_identifier,
                        'interval_count' => $plan->interval_count,
                        'meter' => $stripeMeterId,
                    ],
                    'tiers_mode' => $tiersMode,
                    'tiers' => $tiers,
                ])->id;

                $this->planService->addPaymentProviderPriceId($planPrice, $paymentProvider, $tierPriceId, PaymentProviderPlanPriceType::USAGE_BASED_PRICE);

                $results[PaymentProviderPlanPriceType::USAGE_BASED_PRICE->value] = $tierPriceId;
            }
        }

        return $results;
    }

    private function buildLineItems(array $stripePrices, Plan $plan): array
    {
        $lineItems = [];
        if ($plan->type === PlanType::FLAT_RATE->value) {
            $lineItems = [
                [
                    'price' => $stripePrices[PaymentProviderPlanPriceType::MAIN_PRICE->value],
                    'quantity' => 1,
                ],
            ];
        } elseif ($plan->type === PlanType::USAGE_BASED->value) {

            if (isset($stripePrices[PaymentProviderPlanPriceType::USAGE_BASED_FIXED_FEE_PRICE->value])) {
                $lineItems[] = [
                    'price' => $stripePrices[PaymentProviderPlanPriceType::USAGE_BASED_FIXED_FEE_PRICE->value],
                    'quantity' => 1,
                ];

            }

            $lineItems[] = [
                'price' => $stripePrices[PaymentProviderPlanPriceType::USAGE_BASED_PRICE->value],
            ];

        }

        return $lineItems;
    }

    private function findOrCreateStripeMeter(Plan $plan, PaymentProvider $paymentProvider): string
    {
        $meter = $plan->meter()->firstOrFail();

        $stripeMeter = $this->planService->getPaymentProviderMeterId($meter, $paymentProvider);

        if ($stripeMeter !== null) {
            return $stripeMeter;
        }

        $stripe = $this->getClient();

        $eventName = Str()->slug($meter->name).'-'.Str::random(5);

        $stripeMeter = $stripe->billing->meters->create([
            'display_name' => $meter->name,
            'event_name' => $eventName,
            'default_aggregation' => ['formula' => 'sum'],
        ]);

        $this->planService->addPaymentProviderMeterId($meter, $paymentProvider, $stripeMeter->id, [
            PlanMeterConstants::STRIPE_METER_EVENT_NAME => $eventName,
        ]);

        return $stripeMeter->id;
    }

    private function findOrCreateStripeOneTimeProductPrice(OneTimeProduct $oneTimeProduct, PaymentProvider $paymentProvider, string $stripeProductId): string
    {
        $productPrice = $this->calculationService->getOneTimeProductPrice($oneTimeProduct);

        $stripeProductPriceId = $this->oneTimeProductService->getPaymentProviderPriceId($productPrice, $paymentProvider);

        if ($stripeProductPriceId !== null) {
            return $stripeProductPriceId;
        }

        $stripe = $this->getClient();

        $stripeProductPriceId = $stripe->prices->create([
            'product' => $stripeProductId,
            'unit_amount' => $productPrice->price,
            'currency' => $productPrice->currency()->firstOrFail()->code,
        ])->id;

        $this->oneTimeProductService->addPaymentProviderPriceId($productPrice, $paymentProvider, $stripeProductPriceId);

        return $stripeProductPriceId;
    }

    public function getName(): string
    {
        return PaymentProvider::where('slug', $this->getSlug())->firstOrFail()->name;
    }

    private function assertProviderIsActive(): PaymentProvider
    {
        $paymentProvider = PaymentProvider::where('slug', $this->getSlug())->firstOrFail();

        if ($paymentProvider->is_active === false) {
            throw new \Exception('Payment provider is not active: '.$this->getSlug());
        }

        return $paymentProvider;
    }

    public function getSupportedPlanTypes(): array
    {
        return [
            PlanType::FLAT_RATE->value,
            PlanType::USAGE_BASED->value,
        ];
    }

    public function reportUsage(Subscription $subscription, int $unitCount): bool
    {
        $this->assertProviderIsActive();

        $stripe = $this->getClient();

        $stripeCustomerId = $subscription->user->stripeData()->firstOrFail()->stripe_customer_id;

        $plan = $subscription->plan;

        $paymentProviderMeter = $this->planService->getPaymentProviderMeter($plan->meter, $subscription->paymentProvider);

        if (! $paymentProviderMeter) {
            Log::error('Payment provider meter not found for meter: '.$plan->meter->name);

            return false;
        }

        $stripeEventName = $paymentProviderMeter->data[PlanMeterConstants::STRIPE_METER_EVENT_NAME] ?? null;

        if (! $stripeEventName) {
            Log::error('Stripe event name not found for meter: '.$plan->meter->name);

            return false;
        }

        try {
            $stripe->billing->meterEvents->create([
                'event_name' => $stripeEventName,
                'payload' => [
                    'value' => $unitCount,
                    'stripe_customer_id' => $stripeCustomerId,
                ],
            ]);
        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }

    public function supportsSkippingTrial(): bool
    {
        return true;
    }


    public function getPaymentMethods(User $user): array {
        $paymentProvider = $this->assertProviderIsActive();
        try {
            $stripe = $this->getClient();
            $stripeData = $user->stripeData()->first();
            if(!$stripeData) {
                return [];
            }
            $stripeCustomerId = $stripeData->stripe_customer_id;
            $customer = $stripe->customers->retrieve($stripeCustomerId, ['expand' => ['invoice_settings.default_payment_method']]);
            $defaultStripePaymentMethodId = $customer->invoice_settings->default_payment_method ? $customer->invoice_settings->default_payment_method->id : null;
            $paymentMethods = $stripe->paymentMethods->all([
                'customer' => $stripeCustomerId,
                'type' => 'card',
            ]);

            return collect($paymentMethods->data)->map(function ($paymentMethod) use( $defaultStripePaymentMethodId) {
                return [
                    'id' => $paymentMethod->id,
                    'brand' => $paymentMethod->card->brand,
                    'last4' => $paymentMethod->card->last4,
                    'exp_month' => $paymentMethod->card->exp_month,
                    'exp_year' => $paymentMethod->card->exp_year,
                    'is_valid' => $this->isCardValid([
                        'exp_month' => $paymentMethod->card->exp_month,
                        'exp_year' => $paymentMethod->card->exp_year,
                    ]),
                    'is_default' => $paymentMethod->id === $defaultStripePaymentMethodId,
                ];
            })->toArray();

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());
        }
        return [];
    }
    private function isCardValid(array $card): bool
    {
        $expYear = (int)$card['exp_year'];
        $expMonth = (int)$card['exp_month'];
        $currentYear = (int)date('Y');
        $currentMonth = (int)date('m');
        if ($expYear > $currentYear || ($expYear === $currentYear && $expMonth >= $currentMonth)) {
            return true;
        }
        return false;

    }

    public function updateDefaultPaymentMethod(User $user, string $paymentMethodId): bool
    {
        $this->assertProviderIsActive();

        try {
            $stripe = $this->getClient();
            $stripeData = $user->stripeData()->firstOrFail();
            $stripeCustomerId = $stripeData->stripe_customer_id;


            $stripe->customers->update($stripeCustomerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);

            $subscriptions =  $stripe->subscriptions->all([
                'customer' => $stripeCustomerId,
                'status' => 'active',
                'limit' => 100,
            ]);

            foreach ($subscriptions->autoPagingIterator() as $subscription) {
                $stripe->subscriptions->update($subscription->id, [
                    'default_payment_method' => $paymentMethodId,
                ]);
            }

        } catch (ApiErrorException $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }

    private function getSubscriptionCheckoutCancelUrl(Plan $plan, Subscription $subscription)
    {
        if ($subscription->type === SubscriptionType::LOCALLY_MANAGED) {
            return route('checkout.convert-local-subscription', ['subscriptionUuid' => $subscription->uuid]);
        }

        return route('checkout.subscription', ['planSlug' => $plan->slug]);
    }

    private function getSubscriptionCheckoutSuccessUrl(Subscription $subscription)
    {
        if ($subscription->type === SubscriptionType::LOCALLY_MANAGED) {
            return route('checkout.convert-local-subscription.success');
        }

        return route('checkout.subscription.success');
    }
}
