<?php

namespace App\Services;

use App\Models\IpLocation;
use Illuminate\Support\Facades\Http;

class IpLocationService
{
  public function getLocationByIp($ip): array
  {
    if (!$ip) return [];
    $location = IpLocation::where('ip', $ip)->first();

    if (!$location) {
      $ipLocationUrl = str_replace('{ip}', $ip, config('services.ip_location.url'));
      $response = Http::get($ipLocationUrl);

      if ($response->ok() && $response['status'] === 'success') {
        $location = IpLocation::create([
          'ip' => $ip,
          'data_json' => $response->json(),
        ]);
      } else {
        return ['error' => $response['message'] ?? 'Failed to fetch IP location'];
      }
    }

    return $location->data_json;
  }
}
