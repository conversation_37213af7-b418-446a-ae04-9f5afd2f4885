{"name": "laravel/laravel", "type": "project", "description": "TallStack is the best starter kit (boilerplate) for your next SaaS project.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "abdelhamiderrahmouni/filament-monaco-editor": "^0.2.5", "aws/aws-sdk-php": "^3.293", "blade-ui-kit/blade-icons": "^1.5", "calebporzio/sushi": "^2.5", "codeisawesomehq/filament-tinyeditor": "^1.4", "filament/filament": "^3.3", "filament/notifications": "^3.3", "filament/spatie-laravel-media-library-plugin": "^3.2", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^3.4", "jeffgreco13/filament-breezy": "^2.6", "laragear/two-factor": "^3.0", "laravel/framework": "^12.0", "laravel/horizon": "^5.21", "laravel/pail": "1.x-dev", "laravel/reverb": "^1.5", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.6", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "livewire/livewire": "^3.5", "mews/purifier": "^3.4", "mongodb/laravel-mongodb": "^5.3", "mpociot/versionable": "^4.4", "propaganistas/laravel-phone": "^5.3", "resend/resend-php": "^0.12.0", "saasykit/filament-country-field": "^1.0", "saasykit/laravel-invoices": "^1.0", "saasykit/laravel-money": "^1.0", "saasykit/laravel-open-graphy": "^1.2", "saasykit/laravel-recaptcha": "^1.0", "spatie/laravel-cookie-consent": "^3.3", "spatie/laravel-flash": "^1.9", "spatie/laravel-permission": "^6.0", "spatie/laravel-sitemap": "^7.0", "stechstudio/filament-impersonate": "^3.8", "stripe/stripe-php": "^17.0", "symfony/http-client": "^7.0", "symfony/mailgun-mailer": "^7.0", "symfony/postmark-mailer": "^7.0", "twilio/sdk": "^8.3", "ysfkaya/filament-phone-input": "^3.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "deployer/deployer": "7.3", "doctrine/dbal": "^3.6", "fakerphp/faker": "^1.9.1", "kkomelin/laravel-translatable-string-exporter": "^1.22", "larastan/larastan": "^3.0", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "laravel/telescope": "^5.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "App\\Helpers\\": "app/Helpers/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "clear-caches": ["@php artisan config:clear", "@php artisan cache:clear", "@php artisan route:clear", "@php artisan view:clear", "@php artisan optimize:clear", "@php artisan filament:optimize-clear", "@php artisan filament:clear-cached-components"], "build:ready": ["@clear-caches", "@php -r \"file_put_contents('storage/framework/build.flag', time());\""], "dev": ["@build:ready", "Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74,#312370\" \"php artisan serve --port=5500\" \"php artisan queue:work\"  \"npm run dev\" \"php artisan reverb:start --port=8002 --debug\" \"php artisan pail --timeout=0\"  --names=server,queue,vite,reverb,logs"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}