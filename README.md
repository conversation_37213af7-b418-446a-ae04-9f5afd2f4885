<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://saasykit.com/images/logo-dark.png" width="400" alt="Laravel Logo"></a></p>

## About TallStack
**TallStack** is a SaaS starter kit (boilerplate) that comes packed with all components required to run a modern SaaS software.

**TallStack** is built using the beautiful Laravel framework (using [TALL](https://tallstack.dev/)) and offers an intuitive Filament admin panel that houses all the pre-built components like product, plans, discounts, payment providers, email providers, transactions, blog, user & role management, and much more.

**TallStack** is developer-friendly, uses best coding practices, comes with an ever-growing automated tests that cover the critical components that it offers.

## Features in a nutshell

* Customize Styles: Customize the styles & colors, error page of your application to fit your brand.
* Product, Plans & Pricing: Create and manage your products, plans, and pricing from a beautiful and easy-to-use admin panel.
* Beautiful checkout process: Your customers can subscribe to your plans from a beautiful checkout process.
* Huge list of ready-to-use components: Plans & Pricing, hero section, features section, testimonials, FAQ, Call to action, tab slider, and much more.
* User authentication: Comes with user authentication out of the box, whether classic email/password or social login (Google, Facebook, Twitter, Github, LinkedIn, and more).
* Discounts: Create and manage your discounts and reward your customers.
* SaaS metric stats: View your MRR, Churn rates, ARPU, and other SaaS metrics.
* Multiple payment providers: Stripe, Paddle, and more coming soon.
* Multiple email providers: Mailgun, Postmark, Amazon SES, and more coming soon.
* Blog: Create and manage your blog posts.
* User & Role Management: Create and manage your users and roles, and assign permissions to your users.
* Fully translatable: Translate your application to any language you want.
* Sitemap & SEO: Sitemap and SEO optimization out of the box.
* Admin Panel: Manage your SaaS application from a beautiful admin panel powered by [Filament](https://filamentphp.com/).
* User Dashboard: Your customers can manage their subscriptions, change payment method, upgrade plan, cancel subscription, and more from a beautiful user dashboard powered by [Filament](https://filamentphp.com/).
* Automated Tests: Comes with automated tests for critical components of the application.
* One-line deployment: Provision your server and deploy your application easily with integrated [Deployer](https://deployer.org/) support.
* Developer-friendly: Built with developers in mind, uses best coding practices.
* And much more...

For more details, check the [documentation](https://saasykit.com/docs).

# Installation Guide

This guide will walk you through setting up the `laravel-tall-stack-core` project on your local development environment.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

*   **PHP** (Check `composer.json` for the required version, typically ^8.1 or higher for modern Laravel)
*   **Composer** (PHP dependency manager)
*   **Node.js & NPM** (JavaScript runtime and package manager)
*   **Git** (Version control system)
*   **MySQL** (or your preferred SQL database compatible with Laravel)
*   **MongoDB** (for notifications)

## Setup Steps

1.  **Clone the Repository:**
    Open your terminal and clone the project repository:
    ```bash
    <NAME_EMAIL>:wimse-projects/laravel-tall-stack-core.git
    cd laravel-tall-stack-core
    ```

2.  **Configure Environment Variables:**
    Copy the example environment file and then customize it for your local setup:
    ```bash
    cp .env.example .env
    ```
    Open the `.env` file in your editor and update the following sections:

    *   **Application Key:** This will be generated in a later step.
    *   **Database Credentials (MySQL):**
        ```ini
        DB_CONNECTION=mysql
        DB_HOST=127.0.0.1 
        DB_PORT=3306
        DB_DATABASE=laravel_tall_core 
        DB_USERNAME=root           
        DB_PASSWORD=your_password   
        ```
        *Ensure the `DB_DATABASE` you specify exists on your MySQL server, or create it.*

    *   **MongoDB Credentials (for Notifications):**
        ```ini
        DB_MONGO_CONNECTION=mongodb
        DB_MONGO_HOST=127.0.0.1
        DB_MONGO_PORT=27017
        DB_MONGO_DATABASE=dev_notifications_db 
        DB_MONGO_USERNAME= 
        DB_MONGO_PASSWORD= 
        ```
        *Ensure your MongoDB server is running and accessible.*


3.  **Install PHP Dependencies:**
    Install the required PHP packages using Composer:
    ```bash
    composer install
    ```
    *   **Alternative (for specific environments or CI):** If you encounter platform requirement issues or are in a CI environment, you might use:
        ```bash
        composer install --ignore-platform-reqs --no-interaction --no-scripts --prefer-dist
        ```
        *(Note: `--ignore-platform-reqs` should generally be avoided in local development if possible, as it can hide compatibility issues.)*

4.  **Generate Application Key:**
    This key is used for encryption and needs to be set:
    ```bash
    php artisan key:generate
    ```

5.  **Run Database Migrations:**
    Create the necessary tables in your MySQL database:
    ```bash
    php artisan migrate
    ```

6.  **Seed the Database (Optional):**
    If the project includes seeders to populate initial data:
    ```bash
    php artisan db:seed
    ```

    Initialize demo data (optional):
    ```bash
    php artisan db:seed "Database\Seeders\Demo\DemoDatabaseSeeder"
    ```

7.  **Link Storage Directory:**
    Create a symbolic link from `public/storage` to `storage/app/public`:
    ```bash
    php artisan storage:link
    ```

8.  **Install JavaScript Dependencies:**
    Install the required Node.js packages:
    ```bash
    npm install
    ```

9.  **Build Frontend Assets:**
    Compile your frontend assets (CSS, JavaScript) using Vite (or Laravel Mix if it's an older project):
    ```bash
    npm run build
    ```

## Running the Application

You have a couple of options to run the application for development:

**Option 1: Separate Processes **

*   **Before start the App cleaning cache is needed**
    ```bash
    php artisan optimize:clear
    ```

*   **Start the PHP Development Server:**
    ```bash
    php artisan serve
    ```
    *(This will typically serve your application at `http://localhost:5500` or the `APP_URL` you configured.)*

*   **Start the Vite Development Server (for Hot Module Replacement):**
    In a **new terminal window/tab**, run:
    ```bash
    npm run dev
    ```

**Option 2: Using `composer run dev` (Recommended for Development)**

```bash
composer run dev
```
---

You should now be able to access your application in your web browser! `http://localhost:5500`.

# Pulling Updates from the Base Template (Git)

## Step 1: Add the Template as a Remote (One-Time Setup)
```bash
git remote <NAME_EMAIL>:wimse-projects/laravel-tall-stack-core.git
```

## Step 2: Fetch and Merge Updates

```bash
# Fetch all branches and tags from the template remote
git fetch template

# Merge the changes from the template's main branch into your current branch
git merge template/main --allow-unrelated-histories
```
