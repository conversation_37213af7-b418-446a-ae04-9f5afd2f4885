services:
    laravel:
        container_name: laravel-tall-stack-core
        build:
            context: .
            dockerfile: ./docker/local/Dockerfile.dev
            args:
                WWWGROUP: '${WWWGROUP}'
        ports:
            - '5500:80'
            - '5501:8081'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        network_mode: bridge
        env_file:
            - .env
        volumes: 
            - ./.env:/var/www/html/.env:ro
