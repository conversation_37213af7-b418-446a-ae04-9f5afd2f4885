<?php

use App\Http\Controllers\Api\NotificationController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ProjectItemController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('/payments-providers/stripe/webhook', [
    App\Http\Controllers\PaymentProviders\StripeController::class,
    'handleWebhook',
])->name('payments-providers.stripe.webhook');

Route::post('/payments-providers/paddle/webhook', [
    App\Http\Controllers\PaymentProviders\PaddleController::class,
    'handleWebhook',
])->name('payments-providers.paddle.webhook');

Route::post('/payments-providers/lemon-squeezy/webhook', [
    App\Http\Controllers\PaymentProviders\LemonSqueezyController::class,
    'handleWebhook',
])->name('payments-providers.lemon-squeezy.webhook');

Route::post(
    '/notify',
    [NotificationController::class, 'send']
);

Route::get('/ip-location/{ip}', [
    App\Http\Controllers\Api\IpLocationController::class,
    'getLocationByIp',
])->name('ip-location');

Route::get('/projects/types', [ProjectController::class, 'getProjectTypes']);
Route::get('/projects-item/types', [ProjectItemController::class, 'getProjectItemTypes']);

// Protected routes — user must be authenticated
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('projects', ProjectController::class);
    Route::apiResource('project-items', ProjectItemController::class);
});

Route::middleware('auth:sanctum')->get('/me', function () {
    return response()->json([
        'user' => auth()->user(),
        'session_id' => session()->getId(),
        'session' => session()->all()
    ]);
});
Route::get('/test-user', function () {
    return response()->json([
        'user' => auth()->user(),
    ]);
});
