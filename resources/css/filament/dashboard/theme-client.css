@import "../../../../vendor/filament/filament/resources/css/theme.css";
@import "../../flag-icons.min.css";

@config './tailwind.config.js';

.fi-body {
    @apply !bg-white
}

.fi-layout {
    display: flex;
    flex-direction: row-reverse;
    margin-top: 1%;
    @apply px-4 mx-auto max-w-7xl sm:px-6 lg:px-8
}

.fi-layout .fi-main {
    margin: 0px !important;
    width: 100% !important;
    max-width: 100% !important;
    @apply !px-0 !pl-10
}

/* .fi-layout .fi-main .fi-page {
    border: 1px solid #0065a2;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
    border-radius: 4px;
} */

.fi-layout .fi-sidebar {
    position: relative !important;
}

.fi-sidebar {
    @apply bg-[#fafafa];
}

.fi-sidebar-nav-groups {
    display: none !important;
}

.bg-custom-menu-left {
    background: #efeeed !important;
}

.fi-sidebar-nav {
    padding: 0px !important;
    scrollbar-gutter: auto !important;
    row-gap: normal !important;
    background: white;
}

.fi-icon-btn {
    display: none !important;
}

.fi-custom-menu-container {
    border-top: 2px solid white;
    padding-left: 25px;
    padding-top: 5px;
}

.fi-custom-menu-container-end {
    padding-bottom: 0px;
    padding-top: 10px;
}

.fi-custom-menu-container span {
    font-family: "Roboto Condensed", Arial, sans-serif;
    font-weight: 800;
    font-style: normal;
    font-size: 17px;
    letter-spacing: -0.5px;
}

.fi-custom-menu-container .x-icon-image {
    width: 14px !important;
    margin: 0 9px;
}

@media screen and (max-width: 1500px) {
    /* .fi-layout .fi-main .fi-page{
        padding-left: 2%;
        padding-right: 2%;
    } */
}

@media screen and (max-width: 768px) {
    /* .fi-layout .fi-main .fi-page{
        padding-left: 2%;
        padding-right: 2%;
    } */
}

@media screen and (max-width: 480px) {

    /* .fi-layout .fi-main .fi-page{
        padding-left: 2%;
        padding-right: 2%;
    } */
}

.fi-page {
    section {
        @apply !py-0;
    }
}

/* Filament Button */
.fi-btn {
    @apply !bg-primary_ui_high-900 !rounded !text-lg !uppercase !py-1;
}

.fi-btn.danger-btn {
    @apply !bg-red-600;
}

/* Filament Select label */
.fi-fo-field-wrp-label {
    span {
        @apply !text-black;
        @apply !text-lg;
    }
}

.fi-fo-checkbox-list-option-label, .fi-input, .fi-select-input {
        @apply !text-black;
}

.fi-checkbox-input {
        border: 1px solid #000 !important;
}

/* Filament Select label */
.fi-checkbox-input {
    @apply rounded border-[1px] bg-white shadow-sm ring-1 transition duration-75 checked:ring-0 focus:ring-2 focus:ring-offset-0 disabled:pointer-events-none disabled:bg-gray-50 disabled:text-gray-50 disabled:checked:bg-gray-400 disabled:checked:text-gray-400 dark:bg-white/5 dark:disabled:bg-transparent dark:disabled:checked:bg-gray-600 text-primary_ui_high-900 ring-gray-950/10 focus:ring-primary_ui_high-900 checked:focus:text-primary_ui_high-900 dark:text-primary_ui_high-900 dark:ring-white/20 dark:checked:bg-primary_ui_high-900 dark:focus:ring-primary_ui_high-900 dark:checked:focus:ring-primary_ui_high-900 dark:disabled:ring-white/10 mt-1
}

.information-address, .social {
    @apply !rounded-none !border-none !ring-0 !bg-transparent !shadow-none;
    
    .fi-section {
        @apply !rounded-none !border-none !ring-0 !bg-transparent !shadow-none;
    }

    header.fi-section-header {
        @apply !p-0 mb-3;
    }

    div.fi-section-content-ctn  {
        @apply !border-t-0;
    }

    div.fi-section-content {
        @apply !border-t-0 !p-0;
    }
}

.social {
    @apply !bg-gray-100;
}