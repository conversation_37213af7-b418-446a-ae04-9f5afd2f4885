@import './components/header.scss';
@import './subscription-settings.scss';
#app {
    min-height: 100vh;
}

.ul-lang {
    li {
        a {
            padding-left: 4px !important;
            padding-right: 4px !important;
            padding-top: 4px !important;
            padding-bottom: 4px !important;
        }
    }
}
button{
    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }
}

.square-avatar-upload .fi-fo-preview-image,
.square-avatar-upload img
.square-avatar-upload .filepond--root {
    border-radius: 0 !important;
}

.fme-wrapper {
    .fme-control-section{
        justify-content: end;
    }
    &:not(.fme-full-screen) {
        .fme-control-section {
            margin-top: -40px;
        }
    }
}

.shadow-big {
    box-shadow: 0 0 20px -3px rgba(0,0,0,.75);
}

[x-cloak] { display: none !important; }

.testimonial {
    p {
        margin: 3px 0;
    }
}
