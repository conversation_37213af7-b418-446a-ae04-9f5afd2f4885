@props(['product'])
<div class="{{ $product['recommande'] ? 'bg-primary_ui-50 text-white' : 'bg-white text-primary_ui-50' }} rounded shadow-2xl p-6 pt-2">
    <div class="flex-grow flex flex-col justify-start">    
        <div class="text-center mb-6">
            <div class="h-16 flex items-start justify-center px-3 py-3">
                <h3 class="text-2xl font-bold {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} uppercase leading-tight tracking-tight">
                {{ $product['name'] == 'Gratuit' ? t('ui_product_free') : $product['name'] }}
                </h3>
            </div>
            <div class="h-16 flex items-center justify-center mt-2">
                @if($product['slug'])
                    <a href="/checkout/plan/{{ $product['slug'] }}" class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'text-yellow_custom-50 bg-primary_ui-50' }} px-4 py-2 rounded-md text-2xl font-bold">
                            {{ number_format($product['price'], 0) }}€ / {{ $product['type'] == 'Monthly' ? t('ui_sub_monthly_2') : t('ui_sub_yearly_2') }}
                    </a>
                @else
                    <a href="/register"  class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'text-yellow_custom-50 bg-primary_ui-50' }} px-4 py-2 rounded-md text-2xl font-bold">
                            {{ number_format($product['price'], 0) }}€ / {{ $product['type'] == 'Monthly' ? t('ui_sub_monthly_2') : t('ui_sub_yearly_2') }}
                    </a>
                @endif
            </div>
            <div class="h-16 flex items-center justify-center">
                <p class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold text-center leading-normal tracking-normal">
                @if($product['type'] == 'Monthly')
                    {{ $product['name'] == 'Gratuit' ? t('ui_product_description_free') : t('ui_sub_desc_product_'.$product['description_translation'].'-monthly') }}
                @else
                    {{ $product['name'] == 'Gratuit' ? t('ui_product_description_free') : t('ui_sub_desc_product_'.$product['description_translation'].'-yearly') }}
                @endif
                </p>
            </div>
        </div>

        <div class="space-y-3 mb-6">
            <div 
                class="h-15 text-center text-sm font-bold 
                {{ $product['recommande'] ? 'text-white border-white' : 'text-primary_ui-50 border-primary_ui-50' }} 
                border-t pt-2 border-b pb-2"
            >
                @if ($product['dossier'] > 1)
                    {{ $product['dossier'] }} {{ t('ui_sub_credits_per_project') }} {{ $product['type'] == 'Monthly' ? t('ui_sub_project_monthly') : t('ui_sub_project_yearly') }} <br>
                    <span class="text-xs">
                        @if(isset($product['price'], $product['dossier']) && is_numeric($product['price']) && is_numeric($product['dossier']) && $product['dossier'] != 0)
                            {{ t('ui_sub_either') }} {{ number_format($product['price'] / $product['dossier'], 2) }} € {{ t('ui_sub_each_project') }}
                        @else
                            <span class=" {{ $product['recommande'] ? 'text-primary_ui-50' : 'text-white' }}">-</span>
                        @endif
                    </span>
                @else
                    {{ t('ui_sub_one_credit') }}<br>
                    <span class=" {{ $product['recommande'] ? 'text-primary_ui-50' : 'text-white' }}">-</span>
                @endif
            </div>
            <div class="flex items-start space-x-2">
                <div class="w-5 h-5 flex-shrink-0">
                    <img  style="width: 17px;height: 25px !important;height: 20px !important;}" src="/images/icon/check.svg" />
                </div>
                <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                    {{ $product['user_count'] }} {{ $product['user_count'] === 1 ? t('ui_sub_user') : t('ui_sub_users') }}
                </span>
            </div>
            @foreach ($product['features'] as $feature)
                <div class="flex items-start space-x-2">
                    <div class="w-5 h-5 flex-shrink-0">
                        <img  style="width: 17px;margin-top: -2px;height: 25px !important;height: 20px !important;}" src="/images/icon/<?= $feature['enabled'] ? 'check.svg' : 'x-cross.png' ?>" />
                    </div>
                    <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                        {{ t('ui_sub_feature_'.$feature['key']) }}
                    </span>
                </div>
            @endforeach
        </div>
    </div>
    @if($product['slug'])
        <a href="/checkout/plan/{{ $product['slug'] }}" 
            class="w-full {{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'bg-primary_ui-50 text-white' }} py-3 px-4 rounded-lg font-semibold transition-colors text-center block">
            {{  t('ui_sub_subscribe') }}
        </a>
    @else
        <a  href="/register" 
            class="w-full {{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'bg-primary_ui-50 text-white' }} py-3 px-4 rounded-lg font-semibold transition-colors text-center block">
            {{  t('ui_sub_register') }}
        </a>
    @endif
</div>