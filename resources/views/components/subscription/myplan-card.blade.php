@props(['product', 'current'])
<div class="{{ $current 
    ? 'cursor-pointer group rounded-lg p-5 pt-2 transition transform duration-300 ease-in-out bg-primary_ui-50 text-white scale-[1.02]' 
    : 'cursor-pointer group rounded-lg p-5 pt-2 transition transform duration-300 ease-in-out bg-white text-black hover:bg-primary_ui-50 hover:text-white hover:scale-[1.02]' }}">
    <div class="flex-grow flex flex-col justify-start"> 
        <label class="h-16 flex items-center space-x-3 cursor-pointer mb-4">
            <span class="w-6 h-6 rounded-full border border-blue-700 bg-white inline-block relative">
            </span>
            <span class="font-normal leading-tight">
                <span>{{ $product['name'] }}</span><br>
                €{{ number_format($product['price'], 0) }}
            </span>
        </label>


        <div class="space-y-3">
            @foreach ($product['features'] as $feature)
                <div class="flex items-start space-x-2">
                    <div class="w-5 h-5 flex-shrink-0">
                        @if ($feature['enabled'])
                        <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 512 512">
                            <path d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z"/>
                        </svg>
                        @else
                        <svg class="w-5 h-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 512 512">
                            <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm101.8-262.2L295.6 256l62.2 62.2c4.7 4.7 4.7 12.3 0 17l-22.6 22.6c-4.7 4.7-12.3 4.7-17 0L256 295.6l-62.2 62.2c-4.7 4.7-12.3 4.7-17 0l-22.6-22.6c-4.7-4.7-4.7-12.3 0-17l62.2-62.2-62.2-62.2c-4.7-4.7-4.7-12.3 0-17l22.6-22.6c4.7-4.7 12.3-4.7 17 0l62.2 62.2 62.2-62.2c4.7-4.7 12.3-4.7 17 0l22.6 22.6c4.7 4.7 4.7 12.3 0 17z"/>
                        </svg>
                        @endif
                    </div>
                    <span class="text-sm font-bold ml-2 transition-colors duration-300 {{ $current ? 'text-white' : 'text-primary_ui-50 group-hover:text-white' }}">
                        {{ t('ui_sub_feature_'.$feature['key']) }}
                    </span>
                </div>
            @endforeach
        </div>
    </div>
</div>
