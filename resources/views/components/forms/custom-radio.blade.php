@props([
    'model',
    'value',
    'size' => 'md',
    'class' => '',
    'disabled' => 'false',
])

@php
    $isDisabled = isset($disabled) && $disabled === 'true';
    $sizes = [
        'lg' => [
            'labelClass' => 'ml-3 text-3xl font-semibold',
            'custRadioClass' => 'h-10 w-10 border-[2px]',
            'custRadioInnerClass' => 'h-7 w-7',
        ],
        'md' => [
            'labelClass' => 'ml-1 text-lg',
            'custRadioClass' => 'h-5 w-5 border-[1px]',
            'custRadioInnerClass' => 'h-3 w-3',
        ],
    ];

    $labelClass = $sizes[$size]['labelClass'] ?? '';
    $custRadioClass = $sizes[$size]['custRadioClass'] ?? '';
    $custRadioInnerClass = $sizes[$size]['custRadioInnerClass'] ?? '';
@endphp

<label class="flex cursor-pointer items-center {{ $class }}">
    <input
        type="radio"
        @if ($isDisabled)
            disabled
        @endif
        x-model="{{ $model }}"
        value="{{ $value }}"
        class="sr-only" />

    <span class="flex items-center justify-center rounded-full border-primary_ui-50 transition cust-radio {{ $custRadioClass }}">
        <span x-show="{{ $model }} === '{{ $value }}'" x-cloak
              class="rounded-full bg-primary_ui-50 cust-radio-inner {{ $custRadioInnerClass }}"></span>
    </span>

    <span class="text-black input-label {{ $labelClass }}">
        {{ $slot }}
    </span>
</label>
