@props(['styles' => []])

<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SCSS compiled CSS -->
    @vite(['resources/sass/components/dashboard-main.scss'])
    @if(isset($styles) && is_array($styles))
        @foreach ($styles as $style)
            @vite([$style])
        @endforeach
    @endif

    @include('components.layouts.partials.head')
</head>
<body class="text-primary-900 flex flex-col min-h-screen">
    <div id="app" class="flex flex-col flex-grow">
        <x-layouts.partials.dashboard.custom-header class="flex-shrink-0"/>
        <div class="w-full mx-auto px-4 max-w-7xl sm:px-6 lg:px-8 pt-3 flex-grow">
            <div class="flex flex-col-reverse flex-wrap md:flex-nowrap md:flex-row">
                 <div class="w-full">
                    {{$content}}
                 </div>
            </div>
        </div>
        {{-- <x-layouts.app.footer/> --}}
        @include('components.layouts.partials.tail')
    </div>
</body>
</html>
