@stack('tail')

@vite(['resources/js/app.js'])

@include('components.layouts.partials.analytics')

<script
    src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps_api_key') }}&callback=Function.prototype&libraries=geometry"
    async
    defer
></script>

@php($skipCookieContentBar = $skipCookieContentBar ?? false)

@if (!$skipCookieContentBar)
    @include('cookie-consent::index')
@endif

@livewireScripts
