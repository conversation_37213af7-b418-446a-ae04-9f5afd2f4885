@php($description = isset($metaDescription) ? $metaDescription : config('app.description'))
<meta name="description" content="{{ $description }}">

@php($canonical = isset($canonical) ? $canonical : url()->current())
<link rel="canonical" href="{{ $canonical }}">

<title>
    @isset($title)
        {{ $title }} | {{ config('app.name', 'TallStack') }}
    @else
        {{ config('app.name', 'TallStack') }}
    @endisset
</title>

<link rel="shortcut icon" type="image/x-icon" href="{{asset('images/eco-solar-logo.png')}}">

@include('components.layouts.partials.social-cards')

<!-- Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
{{--<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">--}}

<!-- Styles CMS Begin -->
@isset($style)
    <style>
        {{ $style }}
    </style>
@endisset
<!-- Styles CMS End -->

<!-- Scripts -->
@vite(['resources/sass/app.scss', 'resources/sass/global.scss'])

@stack('head')

@livewireStyles
