@php
    $user = auth()->user();
    $companyLogoUrl = $user ? $user->getCompanyLogoUrl() : null;
    $avatarUrl = $user ? $user->getFilamentAvatarUrl() : null;
@endphp

<header
    class="dashboard-header  border-gray-200 bg-primary_ui_high-50 z-[99] text-lg sticky top-0"
    x-data="{ open: false }"
    x-cloak
>
    <div class="flex items-center justify-between h-24 px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        {{-- Left side: Logo / Brand ------------------------------------------------}}
        <div class="flex items-center h-full">
            <a href="{{ url('/') }}" class="flex items-center space-x-2">
                {{-- <img src="{{ Vite::asset('resources/images/logo.svg') }}"
                     alt="Logo"
                     class="h-8 w-auto"> --}}
                <span class="font-bold text-2xl text-white ">
                    {{ config('app.name') }}
                </span>
            </a>
            @if($user)
                @php
                    $projects = collect([
                        ['id' => 1, 'label' => 'AUTONOMIE REVENTE'],
                        ['id' => 2, 'label' => 'ParWattsIssue'],
                        ['id' => 3, 'label' => 'Test414'],
                        ['id' => 4, 'label' => 'ExtraProject1'],
                        ['id' => 5, 'label' => 'ExtraProject2'],
                        ['id' => 6, 'label' => 'ExtraProject3'],
                        ['id' => 7, 'label' => 'ExtraProject4'],
                        ['id' => 8, 'label' => 'ExtraProject5'],
                        ['id' => 9, 'label' => 'ExtraProject6'],
                        ['id' => 10, 'label' => 'ExtraProject7'],
                    ]);
                @endphp

                <div x-data="{ showMore: false }" class="relative ml-6 h-full">
                    <button
                        class="folder-dropdown-trigger h-full flex items-center space-x-2 text-white focus:outline-none"
                        @mouseleave="showMore = false"
                    >
                        <img src="{{ asset('images/menu/folder-icon.png') }}" width="32" />
                        <svg class="folder-icon-chevron" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m6 9 6 6 6-6"></path>
                        </svg>
                        <span class="font-bold">Projects</span>
                    </button>

                    <div
                        class="folder-dropdown absolute mt-[-10px] w-64 text-gray-900 rounded-none z-50"
                        @mouseleave="showMore = false"
                    >
                        <div class="text-sm font-semibold">
                            <ul class="bg-white shadow-big px-5 py-3 space-y-1">
                                <template x-for="(project, index) in (showMore ? {{ $projects->toJson() }} : {{ $projects->slice(0, 3)->toJson() }})" :key="project.id">
                                    <li class="pb-1 cursor-pointer hover:font-bold" x-text="project.label"></li>
                                </template>
                                <li class="pb-1">
                                    <button @click="showMore = !showMore" class="text-primary_ui-50 hover:font-bold">
                                        <span x-show="!showMore">See more projects</span>
                                        <span x-show="showMore">See fewer projects</span>
                                    </button>
                                </li>
                                <li>
                                    <a href="/dashboard" class="block text-primary_ui-50 hover:font-bold mt-1">
                                        All projects
                                    </a>
                                </li>
                            </ul>

                            <div class="bg-white shadow-big px-5 py-3 mt-3">
                                <a href="/create-project" class="font-bold block text-primary_ui-50">
                                    Create a project
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

        </div>

        @if($user)
        <div class="flex items-center gap-6 h-full">
            {{-- <div class="relative" x-data="{ open: false }">
                <button
                    @click="open = !open"
                    class="flex items-center space-x-2 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                    <img
                        src="{{ $companyLogoUrl ? $companyLogoUrl :  $avatarUrl }}"
                        alt="{{ $user->getPublicName() }}"
                        class="w-8 h-8 rounded-full"
                    >
                    <span class="sr-only">{{ $user->getPublicName() }}</span>
                </button>

                <div
                    x-show="open"
                    @click.away="open = false"
                    x-transition
                    class="absolute right-0 z-50 mt-2 w-64 bg-white border-gray-200 rounded-md shadow-lg"
                >
                    <ul class="py-1">
                        <li>
                            <a
                                href="{{ route('filament.dashboard.pages.my-profile') }}"
                                class="flex items-center px-4 py-2 text-sm text-gray-700  hover:bg-gray-100"
                            >
                                <x-heroicon-s-user-circle class="w-5 h-5 mr-2" />
                                {{ $user->getPublicName() }}
                            </a>
                        </li>


                        <li class="border-t border-gray-200"></li>

                        <li class="px-4 py-2 text-sm text-gray-700">
                            <span class="font-medium">{{ __('Theme') }}</span>
                            <div class="mt-1 space-y-1">
                                <button
                                    x-data
                                    @click="localStorage.setItem('theme', 'light'); window.dispatchEvent(new CustomEvent('theme-changed', { detail: 'light' }))"
                                    class="w-full text-left px-2 py-1 rounded hover:bg-gray-100"
                                >
                                    {{ __('Light') }}
                                </button>
                                <button
                                    x-data
                                    @click="localStorage.setItem('theme', 'dark'); window.dispatchEvent(new CustomEvent('theme-changed', { detail: 'dark' }))"
                                    class="w-full text-left px-2 py-1 rounded hover:bg-gray-100"
                                >
                                    {{ __('Dark') }}
                                </button>
                                <button
                                    x-data
                                    @click="localStorage.removeItem('theme'); window.dispatchEvent(new CustomEvent('theme-changed', { detail: 'system' }))"
                                    class="w-full text-left px-2 py-1 rounded hover:bg-gray-100"
                                >
                                    {{ __('System') }}
                                </button>
                            </div>
                        </li>

                        <li class="border-t border-gray-200"></li>

                        @if($user->isAdmin())
                            <li>
                                <a
                                    href="{{ route('filament.admin.pages.dashboard') }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                    <x-heroicon-s-cog-8-tooth class="w-5 h-5 mr-2" />
                                    {{ __('Admin Panel') }}
                                </a>
                            </li>
                        @endif

                        @if(config('app.two_factor_auth_enabled'))
                            <li>
                                <a
                                    href="{{ \App\Filament\Dashboard\Pages\TwoFactorAuth\TwoFactorAuth::getUrl() }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                    <x-heroicon-s-cog-8-tooth class="w-5 h-5 mr-2" />
                                    {{ __('2-Factor Authentication') }}
                                </a>
                            </li>
                        @endif

                        <li class="border-t border-gray-200"></li>

                        <li>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button
                                    type="submit"
                                    class="w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                    <x-heroicon-s-arrow-left-on-rectangle class="w-5 h-5 mr-2" />
                                    {{ __('Logout') }}
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div> --}}
            <a href="{{ route('coming-soon') }}">
                <img src="{{ asset('images/menu/question-icon.png') }}" width="32" />
            </a>

            <a href="{{ route('filament.dashboard.pages.my-settings') }}">
                <img src="{{ asset('images/menu/setting-icon.png') }}" width="32" />
            </a>

            <div class="flex items-center">
                <a href="{{ route('filament.dashboard.pages.my-profile') }}" class="uppercase flex items-center gap-2 text-white">
                    <img src="{{ asset('images/menu/user-icon.png') }}" width="32" />

                    <span>{{ $user->getPublicName() }}</span>
                </a>
                <a href="{{ route('filament.dashboard.pages.notifications') }}" class="relative w-5">
                    <span class="absolute -top-8 right-0">
                        <livewire:notifications-count />
                    </span>
                </a>
            </div>


            <form method="POST" action="{{ route('logout') }}" class="h-full">
                @csrf
                <button
                    type="submit"
                    class="w-full h-full uppercase px-3 text-white hover:bg-primary_ui_high-900"
                >
                    {{ __('Logout') }}
                </button>
            </form>
        </div>
        @endif
    </div>
</header>
