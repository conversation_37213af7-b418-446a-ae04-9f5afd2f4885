<header class="sticky top-0 z-[999] shadow-xl bg-white">
  <nav class="text-black">

    <!-- 1) TOP BAR -->
    <div class="max-w-6xl mx-auto flex items-center justify-between px-4 py-2">

      <!-- LEFT: burger / logo / tagline -->
      <div class="flex items-center space-x-4">
        <!-- burger: mobile only -->
        <div x-data="{ open:false }" class="block md:hidden">
          <!-- button -->
          <button @click="open = !open"
                  class="text-primary-950 rounded hover:bg-gray-100 focus:ring-2 focus:ring-gray-200">
            <x-heroicon-o-bars-3 class="w-8 h-8 mr-2" />
          </button>
          <!-- backdrop -->
          <div x-show="open" @click="open=false"
               class="fixed inset-0 bg-black bg-opacity-50 z-40"></div>
          <!-- offcanvas -->
          <aside
            x-show="open"
            x-transition:enter="transition-transform duration-300 ease-out"
            x-transition:enter-start="-translate-x-full"
            x-transition:enter-end="translate-x-0"
            x-transition:leave="transition-transform duration-200 ease-in"
            x-transition:leave-start="translate-x-0"
            x-transition:leave-end="-translate-x-full"
            class="fixed inset-y-0 left-0 z-50 w-full bg-white shadow-lg overflow-y-auto"
            >
            <!-- close bar -->
            <div class="flex items-center justify-between p-4 shadow">
              <a href="/{{ app()->getLocale() }}" class="flex items-center">
                @if(config('app.logo.1'))
                  <img src="{{ asset(config('app.logo.1')) }}" class="h-14" alt="Logo">
                @else
                  <span class="text-xl">{{ config('app.name') }}</span>
                @endif
              </a>
              <button @click="open=false" class="p-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                     viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div class="px-3 py-4 overflow-y-auto">
              <ul tabindex="0">
                <x-layouts.app.navigation-links />
              </ul>
            </div>
          </aside>
        </div>

        <!-- logo always visible -->
        <a href="/{{ app()->getLocale() }}" class="flex justify-center items-center">
            @if (config('app.logo.1'))
                <img src="{{asset(config('app.logo.1') )}}" class="w-[266px] h-auto" alt="Logo" />
            @else
                <span class="text-xl">
                    {{ config('app.name') }}
                </span>
            @endif
        </a>

        <!-- tagline: hide on mobile -->
        <div class="hidden sm:flex flex-col text-primary_ui-50 text-sm sm:text-lg uppercase">
          <span>Contrôle et Expertise</span>
          <span>de votre système photovoltaïque</span>
        </div>
      </div>

      <!-- RIGHT: language + auth -->
      <div class="flex items-center space-x-4">
        <!-- language dropdown -->
        <div x-data="{ open:false }" class="hidden 2sm:flex relative">
          <button @click="open=!open"
                  class="flex items-center text-primary_ui-50 uppercase text-sm">
            {{ strtoupper(app()->getLocale()) }}
            <svg class="ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 483.049 483.049">
              <polygon points="0,121.155 241.524,361.894 241.524,121.155"></polygon>
              <polygon points="241.524,121.155 241.524,361.894 483.049,121.155"></polygon>
            </svg>
          </button>
          <div x-show="open" @click.away="open=false" @keydown.escape.window="open=false"
               x-transition
               class="absolute right-0 mt-2 bg-white shadow z-50">
            <x-layouts.app.language-list />
          </div>
        </div>

        <!-- desktop auth: hidden on mobile -->
        <div class="hidden 2sm:flex items-center space-x-2">
          @auth
            <x-layouts.app.user-menu />
          @else
            <a href="{{ route('register') }}"
               class="px-3 py-1 text-xs uppercase border border-primary_ui-50 rounded text-primary_ui-50 hover:bg-primary_ui-50 hover:text-white transition">
              {{ __('app.navigation.register') }}
            </a>
            <a href="{{ route('login') }}"
               class="px-3 py-1 text-xs uppercase border border-primary_ui-50 rounded text-primary_ui-50 hover:bg-primary_ui-50 hover:text-white transition">
              {{ __('app.navigation.login') }}
            </a>
          @endauth
        </div>
      </div>
    </div>

    <!-- 2) PRIMARY NAV (desktop only) -->
    <div class="hidden 2sm:flex  navbar w-full justify-center h-8 items-center py-0 px-0 min-h-0 bg-primary_ui-50">
      <div class="max-w-6xl mx-auto w-full flex justify-center lg:justify-start">
        <div class="min-w-[266px] mr-[36px] hidden lg:block"></div>
        <x-nav>
          <x-layouts.app.navigation-links />
        </x-nav>
      </div>
    </div>

    <!-- 3) MOBILE AUTH BAR (mobile only) -->
    <div class="flex 2sm:hidden justify-end items-center bg-primary_ui-50 px-4 py-2 gap-4">
        <!-- language dropdown -->
        <div x-data="{ open:false }" class="relative">
          <button @click="open=!open"
                  class="flex items-center text-white uppercase text-sm">
            {{ strtoupper(app()->getLocale()) }}
            <svg class="ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 483.049 483.049">
              <polygon points="0,121.155 241.524,361.894 241.524,121.155"></polygon>
              <polygon points="241.524,121.155 241.524,361.894 483.049,121.155"></polygon>
            </svg>
          </button>
          <div x-show="open" @click.away="open=false" @keydown.escape.window="open=false"
               x-transition
               class="absolute -right-8 mt-2 bg-white shadow z-50">
            <x-layouts.app.language-list />
          </div>
        </div>
      @auth
        <x-layouts.app.user-menu />
      @else
        <a href="{{ route('register') }}"
           class="px-3 py-1 text-xs uppercase border border-white rounded text-white hover:bg-white hover:text-white transition">
          {{ __('app.navigation.register') }}
        </a>
        <a href="{{ route('login') }}"
           class="px-3 py-1 text-xs uppercase border border-white rounded text-white hover:bg-white hover:text-white transition">
          {{ __('app.navigation.login') }}
        </a>
      @endauth
    </div>

  </nav>
</header>