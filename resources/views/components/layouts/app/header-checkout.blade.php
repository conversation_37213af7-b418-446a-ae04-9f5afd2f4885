<div class="sticky top-0 z-[999] shadow-xl shadow-gray-300/50 bg-white">
    <nav class="relative text-black">
        <div class="navbar max-w-6xl w-full justify-between items-center mx-auto">
            <div class="navbar-start flex items-center gap-8">
                <div x-data="{ open: false }" class="flex lg-2:hidden">
                    <button @click="open = !open"
                        type="button"
                        class="inline-flex items-center p-2 mt-2 ms-3 text-sm text-primary-950 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
                        aria-controls="separator-sidebar"
                        :aria-expanded="open"
                        aria-label="Toggle sidebar">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h8m-8 6h16" />
                        </svg>
                    </button>
                    <div x-show="open" @click="open = false"
                        class="fixed inset-0 bg-black bg-opacity-50 z-40"
                        x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0">
                    </div>
                    <aside id="separator-sidebar"
                        x-bind:class="open ? 'translate-x-0' : '-translate-x-full'"
                        class="fixed top-0 left-0 z-40 w-full xs:w-80 h-screen transition-transform duration-300 ease-in-out bg-white shadow-lg shadow-gray-300/50 transform"
                        aria-label="Sidebar">
                        <div class="h-full overflow-y-hidden">
                            <div class="sticky flex justify-between items-center shadow-lg shadow-gray-300/50 px-3 py-2">
                                <a href="/{{ app()->getLocale() }}" class="flex items-center">
                                    <img src="{{asset(config('app.logo.1') )}}" class="w-[266px] h-auto" alt="Logo" />
                                </a>
                                <button @click="open = false"
                                    type="button"
                                    class="inline-flex items-center p-2 text-sm text-primary-950 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
                                    aria-controls="separator-sidebar"
                                    :aria-expanded="open"
                                    aria-label="Toggle sidebar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            <div class="px-3 py-4 overflow-y-auto">
                                <ul tabindex="0">
                                    <x-layouts.app.navigation-links></x-layouts.app.navigation-links>
                                </ul>
                            </div>
                        </div>
                    </aside>
                </div>
                <a href="/{{ app()->getLocale() }}" class="flex justify-center items-center">
                    <img src="{{asset(config('app.logo.1') )}}" class="w-[266px] h-auto" alt="Logo" />
                </a>
                <div class="flex items-start flex-col text-primary_ui-50 text-xl uppercase">
                    <span> Controle et Expertise</span>
                    <span >De votre systeme photovoltaique</span>
                </div>
            </div>
            <div class="navbar-end ">
                <div x-data="{ open: false }" class="relative">
                    <!-- Trigger Button -->
                    <div
                        @click="open = !open"
                        class="btn btn-ghost btn-lang text-primary_ui-50 cursor-pointer"
                    >
                        <span class="font-medium uppercase">
                            {{ strtoupper(config('app.locale')) }}
                        </span>
                        <svg class="ml-1" height="12.5px" width="12.5px" fill="currentColor" viewBox="0 0 483.049 483.049">
                            <polygon points="0,121.155 241.524,361.894 241.524,121.155"></polygon>
                            <polygon points="241.524,121.155 241.524,361.894 483.049,121.155"></polygon>
                        </svg>
                    </div>

                    <!-- Dropdown Content -->
                    <div
                        x-show="open"
                        x-cloak
                        @click.away="open = false"
                        @keydown.escape.window="open = false"
                        x-transition
                        class="absolute right-0 mt-2 z-50"
                    >
                        <x-layouts.app.language-list />
                    </div>
                </div>
                <span>&nbsp;&nbsp;</span>
                <div class="hidden 2sm:block">
                    @auth
                    <x-layouts.app.user-menu></x-layouts.app.user-menu>
                    @else
                        <x-link class="text-primary_ui-50 py-1.5 px-2 font-medium uppercase text-xs border border-primary_ui-50 rounded-md" href="{{route('register')}}">{{ __('app.navigation.register') }}</x-link><span>&nbsp;&nbsp;</span>
                        <x-link class="text-primary_ui-50 py-1.5 px-2 font-medium uppercase text-xs border border-primary_ui-50 rounded-md" href="{{route('login')}}">{{ __('app.navigation.login') }}</x-link>
                    @endauth
                </div>
            </div>
        </div>
        <div class="navbar max-w-full mx-auto text-end items-center pb-0 pt-0 max-h-8 block 2sm:hidden">
            <div>
                @auth
                    <x-layouts.app.user-menu></x-layouts.app.user-menu>
                @else
                    <x-link class="text-primary-950 font-medium uppercase text-xs border border-primary-950 rounded-md" href="{{route('register')}}">{{ __('app.navigation.register') }}</x-link><span>&nbsp;&nbsp;</span>
                    <x-link class="text-primary-950 font-medium uppercase text-xs border border-primary-950 rounded-md" href="{{route('login')}}">{{ __('app.navigation.login') }}</x-link>
                @endauth
            </div>
        </div>
    </nav>
</div>
