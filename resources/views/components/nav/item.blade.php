@props([
    'href' => '#',
    'linkClass' => '',
    'selected' => false
])

@php
    $finalUrl = $href;
    $isAnchor = str_starts_with($href, '#');

    if (!$isAnchor) {
        $finalUrl = url($href);
    }
@endphp

<li {{ $attributes }}>
    <a
        href="{{ $finalUrl }}"
        class="rounded py-2 px-3 text-[14px] h-8 flex items-center w-fit {{ $linkClass }} {{ $selected ? 'bg-yellow_custom-100 !text-primary_ui-50' : 'font-medium sm:text-white' }}"
        style="{{ $selected ? 'font-weight: 700;' : 'font-weight: 500;' }}"
    >
        {{ $slot }}
    </a>
</li>