@props([
    'label' => false,
    'id' => false,
    'name' => '',
    'type' => 'text',
    'value' => false,
    'placeholder' => false,
    'labelClass' => 'text-gray-900',
    'inputClass' => 'text-gray-900 bg-primary-50',
    'required' => false,
    'autofocus' => false,
    'autocomplete' => false,
    'maxWidth' => 'max-w-xs',
    'disabled' => false,
    'revealable' => true,
])

@php
    $id = $id ?? 'text_' . rand();
    $required = $required ? 'required' : '';
    $autofocus = $autofocus ? 'autofocus' : '';
    $value = $value ? 'value="' . $value . '"' : '';
    $autocomplete = $autocomplete ? 'autocomplete="' . $autocomplete . '"' : '';
    $disabled = $disabled ? 'disabled' : '';
@endphp

<label {{ $attributes->merge(['class' => 'form-control w-full ' . $maxWidth]) }} for="{{$id}}">
    @if($label)
        <div class="label">
            <span class="label-text">{{ $label }}</span>
        </div>
    @endif
    @if($type === 'password' && $revealable)
        <div x-data="{ show: false }" class="relative">
            <input :type="show ? 'text' : 'password'"
                   class="input input-bordered input-md w-full {{$maxWidth}} pr-10"
                   placeholder="{{$placeholder}}"
                   name="{{$name}}"
                   {{$required}} {{$autofocus}} {!! $value !!} {!! $autocomplete !!} {{$disabled}} id="{{$id}}">
            <button type="button"
                    class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                    @click="show = !show"
                    tabindex="-1">
                <span x-show="!show" x-cloak>
                    @svg('heroicon-o-eye', 'h-5 w-5')
                </span>
                <span x-show="show" x-cloak>
                    @svg('heroicon-o-eye-slash', 'h-5 w-5')
                </span>
            </button>
        </div>
    @else
        <input type="{{$type}}"  class="input input-bordered input-md w-full {{$maxWidth}}" placeholder="{{$placeholder}}" name="{{$name}}" {{$required}} {{$autofocus}} {!! $value !!} {!! $autocomplete !!} {{$disabled}} id="{{$id}}">
    @endif
</label>
