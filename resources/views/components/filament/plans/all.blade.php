@props([
    'buyRoute' => 'subscription.change-plan',
    'checkoutUrl' => $checkoutUrl,
    'disableBuyBtn' => false,
])

<section
    x-data="{
        userType: 'Particulier',
        billingInterval: '{{ $preselectedIntervalHere }}'
    }"
    class="bg-white dark:bg-gray-900 p-5 dark:text-white"
>
    @if(isset($subscription))
        <div class="mx-auto max-w-screen-md text-center mb-8">
            <h2 class="mb-4 text-xl tracking-tight text-gray-900 dark:text-white">
                {{ __('You are currently on the') }}
                <div class="badge badge-primary badge-outline font-bold text-xl p-5 uppercase">
                    {{ $subscription->plan->product->name }} / {{ $subscription->plan->interval->name }}
                </div>
                {{ __('plan') }}
            </h2>
        </div>
    @endif

    <div class="max-w-2xl mx-auto px-4 mb-3">
        <div class="flex justify-between gap-4 flex-wrap sm:flex-nowrap">
            <div class="plan-switcher tabs tabs-boxed bg-neutral-200 dark:bg-gray-800 max-w-fit">
                <a
                    class="tab dark:text-white"
                    :class="{ 'tab-active': userType === 'Professionnel' }"
                    @click="userType = 'Professionnel'"
                >
                    Professional
                </a>
                <a
                    class="tab dark:text-white"
                    :class="{ 'tab-active': userType === 'Particulier' }"
                    @click="userType = 'Particulier'"
                >
                    Particular
                </a>
            </div>

            <div
                class="plan-switcher tabs tabs-boxed bg-neutral-200 dark:bg-gray-800 max-w-fit"
                x-show="userType === 'Professionnel'"
                x-transition
                :class="{ 'invisible absolute': userType !== 'Professionnel' }"
            >
                @foreach($groupedPlans as $interval => $plans)
                    <a
                        class="tab dark:text-white"
                        :class="{ 'tab-active': billingInterval === '{{ $interval }}' }"
                        @click="billingInterval = '{{ $interval }}'"
                    >
                        {{ str($interval)->title() }}
                    </a>
                @endforeach
            </div>
        </div>
    </div>


    <div x-show="userType === 'Professionnel'">
        @foreach($groupedPlans as $interval => $plans)
            <div
                x-show="billingInterval === '{{ $interval }}'"
                class="grid gap-6 row-gap-4 lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 xl:max-w-screen-lg sm:mx-auto pt-5 pb-5"
            >
                @foreach($plans as $plan)
                    @if($plan->product->account_type === 'Professionnel')
                        <x-filament.plans.one :disableBuyBtn="$disableBuyBtn" :plan="$plan" :subscription="$subscription" :buyRoute="$buyRoute" :checkoutUrl="$checkoutUrl" />
                    @endif
                @endforeach
            </div>
        @endforeach
    </div>


    <div
        x-show="userType === 'Particulier'"
                class="grid gap-6 row-gap-4 lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 xl:max-w-screen-lg sm:mx-auto pt-5 pb-5"
    >
        @foreach($groupedPlans as $interval => $plans)
            @foreach($plans as $plan)
                @if($plan->product->account_type === 'Particulier')
                    <x-filament.plans.one :disableBuyBtn="$disableBuyBtn" :plan="$plan" :subscription="$subscription" :buyRoute="$buyRoute" :checkoutUrl="$checkoutUrl" />
                @endif
            @endforeach
        @endforeach
    </div>

</section>
