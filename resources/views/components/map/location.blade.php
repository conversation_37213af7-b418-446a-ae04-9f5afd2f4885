<div
    class="flex gap-10 relative"
    >
    <div class="flex-1">
        <div class="space-y-3">
            <x-forms.custom-radio model="locationType" value="address">Adresse</x-forms.custom-radio>
            <x-forms.custom-radio model="locationType" value="gps-geolocated">Point GPS géolocalisé</x-forms.custom-radio>
            <x-forms.custom-radio model="locationType" value="gps-point">Point GPS</x-forms.custom-radio>
        </div>

        <div x-show="locationType === 'address' || locationType === 'gps-geolocated'" x-transition x-cloak class="mt-6 space-y-3"
        >
            <input type="text" placeholder="Pays" x-model="address.country" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-5 py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="text" placeholder="Ville" x-model="address.city" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-5 py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="text" placeholder="Rue" x-model="address.street" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-5 py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="text" placeholder="Numéro" x-model="address.number" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-5 py-2 focus:border-primary_ui-50 focus:outline-none" />
        </div>

        <div x-show="locationType === 'gps-point'" x-transition x-cloak class="mt-6 space-y-3">
            <input type="number" step="any" placeholder="Latitude" x-model.number="latitude" :disabled="locationType === 'gps-geolocated'"
                class="w-full text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-5 py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="number" step="any" placeholder="Longitude" x-model.number="longitude" :disabled="locationType === 'gps-geolocated'"
                class="w-full text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-5 py-2 focus:border-primary_ui-50 focus:outline-none" />
        </div>
    </div>

    <div
        class="w-[55%]"
        x-transition
        x-cloak
        {{-- x-init="geolocateUser()" --}}
    >
        <x-map.map-picker :distance-km="0.1"></x-map.map-picker>
    </div>

    <div
        x-show="isLoading"
        x-transition
        x-cloak
        class="absolute inset-0 z-10 flex items-center justify-center rounded-md bg-white/75"
    >
        <span class="h-12 w-12 animate-spin rounded-full border-4 border-x-primary_ui-50"></span>
    </div>
</div>
