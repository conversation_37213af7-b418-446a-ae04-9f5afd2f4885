<span tabindex="0" style="opacity:0; height:0; width:0; position:absolute;"></span>

<div
    x-data="{
        currentStep: 1,
        selectAllFields: false,
        selectAllLanguages: false,
        selectedFields: [],
        selectedLanguages: [],
        loading: false,
        fields: {{ json_encode(array_keys($translatedFields)) }},
        languages: {{ json_encode(array_keys($languages)) }},
        toggleAllFields() {
            this.loading = true;
            setTimeout(() => {
                if (this.selectAllFields) {
                    this.selectedFields = [...this.fields];
                } else {
                    this.selectedFields = [];
                }
                this.loading = false;
            }, 200);
        },
        toggleAllLanguages() {
            this.loading = true;
            setTimeout(() => {
                if (this.selectAllLanguages) {
                    this.selectedLanguages = this.languages.filter(code => code.toLowerCase() !== 'en');
                } else {
                    this.selectedLanguages = [];
                }
                this.loading = false;
            }, 200);
        },
        async doTranslate() {
            this.loading = true;
            await $wire.set('selectedFields', this.selectedFields);
            await $wire.set('selectedLanguages', this.selectedLanguages);
            await $wire.set('selectAllLanguages', this.selectAllLanguages);
            await $wire.doTranslate();
            close();
            this.loading = false;
        },
        closeAutoTranslateModal() {
            close();
        },
        setCurrentStep(step) {
            this.currentStep = step;
        },
    }"
>
    <template x-if="currentStep === 1">
        <div style="max-height: 400px; overflow-y: auto; padding-right: 0.5rem;">
            <div class="ml-2 mb-4 max-w-max">
                <label class="flex items-center space-x-2 cursor-pointer">
                    <small x-show="loading" class="text-gray-500 ml-2">
                        <svg class="animate-spin h-3 w-3 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                        </svg>
                    </small>
                    <input type="checkbox" x-model="selectAllFields" @change="toggleAllFields" :disabled="loading" class="form-checkbox rounded" />
                    <span class="text-medium">{{ __('Check all') }}</span>
                </label>
            </div>
            <div class="grid grid-cols-2 gap-4 ml-6">
                @foreach ($translatedFields as $field => $label)
                    <div class="flex items-center space-x-2">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" :disabled="loading" x-model="selectedFields" value="{{ $field }}" class="form-checkbox rounded cursor-pointer" />
                            <span class="txt-sm">{!! $label !!}</span>
                        </label>
                    </div>
                @endforeach
            </div>
        </div>
    </template>

    <template x-if="currentStep === 2">
        <div style="max-height: 400px; overflow-y: auto; padding-right: 0.5rem;">
            <div class="ml-2 mb-4 max-w-max">
                <label class="flex items-center space-x-2 cursor-pointer">
                    <small x-show="loading" class="text-gray-500 ml-2">
                        <svg class="animate-spin h-3 w-3 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                        </svg>
                    </small>
                    <input type="checkbox" x-model="selectAllLanguages" @change="toggleAllLanguages" :disabled="loading" class="form-checkbox rounded" />
                    <span class="text-medium">{{ __('Check all') }}</span>
                </label>
            </div>
            <div class="grid grid-cols-2 gap-4 ml-6">
                @foreach ($languages as $code => $label)
                    <div class="flex items-center space-x-2">
                        <label class="flex items-center space-x-2 {{ strtolower($code) === 'en' ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' }}">
                            <input type="checkbox" :disabled="loading || '{{ strtolower($code) }}' === 'en'" x-model="selectedLanguages" value="{{ $code }}" class="form-checkbox rounded {{ strtolower($code) === 'en' ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' }}" />
                            <span class="txt-sm">{!! $label !!}</span>
                        </label>
                    </div>
                @endforeach
            </div>
        </div>
    </template>

    <div class="mt-6 flex justify-end">
        <template x-if="currentStep === 1">
            <div>
                <x-filament::button 
                    @click="setCurrentStep(2)"
                    x-bind:disabled="selectedFields.length === 0" 
                >
                    {{ __('Next') }}    
                </x-filament::button>
                <x-filament::button color="gray" @click="closeAutoTranslateModal" class="ml-2">
                    {{ __('Cancel') }}
                </x-filament::button>
            </div>
        </template>
        <template x-if="currentStep === 2">
            <div>
                <x-filament::button 
                    @click="doTranslate()"
                    x-bind:disabled="selectedLanguages.length === 0" 
                >
                    <template x-if="loading">
                        <svg class="animate-spin h-4 w-4 inline-block mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                        </svg>
                    </template>
                    {{ __('Save') }}
                </x-filament::button>
                <x-filament::button color="gray" @click="setCurrentStep(1)" class="ml-2">
                    {{ __('Back') }}
                </x-filament::button>
            </div>
        </template>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    if (document.activeElement instanceof HTMLElement) {
                        document.activeElement.blur();
                    }
                }, 50);
            });
        </script>
    @endpush
</div>
