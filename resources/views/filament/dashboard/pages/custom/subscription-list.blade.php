@php
    use Carbon\Carbon;
    $active = $this->activeSubscription;
    $endsAt = $active && $active->ends_at ? Carbon::parse($active->ends_at)->format('d/m/Y') : null;
    $user_count = $active && $active->plan_json ? $active->plan_json['user_count'] : null;
    $freeProduct = [
        [
            'label' => 'Comparatif Production Réel vs Production PVGIS',
            'enabled' => true,
            'key' => 'prod_reel_vs_prod_pvgis',
        ],
        ['label' => 'Simulation financière SIMPLIFIÉE', 'enabled' => true, 'key' => 'sim_financiere_simplifie'],
        ['label' => 'Profits / pertes / TRI / ROI', 'enabled' => false, 'key' => 'profit_perte_tri_roi'],
        ['label' => 'Contrôle permanent de la production solaire', 'enabled' => false, 'key' => 'control_prod_sol'],
        ['label' => 'Contrôle de l\'économie réalisée sur facture', 'enabled' => false, 'key' => 'control_eco_facture'],
        [
            'label' => 'Dossier de rendement et de rentabilité Impression PDF',
            'enabled' => false,
            'key' => 'rendrement_impression_pdf',
        ],
        ['label' => 'Alerte maintenance Préventive', 'enabled' => false, 'key' => 'alert_prev'],
        ['label' => 'Alerte maintenance Corrective', 'enabled' => false, 'key' => 'alert_corr'],
        ['label' => 'Sauvegarde du dossier', 'enabled' => false, 'key' => 'save_folder'],
        ['label' => 'Support technique en ligne', 'enabled' => false, 'key' => 'tech_support'],
        ['label' => 'Utilisation commerciale autorisée', 'enabled' => false, 'key' => 'use_commercial'],
    ];
@endphp

<x-filament::page>

    @if ($active)
        <div class="space-y-6 p-4 border border-primary_ui_high-50">
            <div class="space-y-6 pl-4 pr-4">
                <div class="grid grid-cols-6">
                    <div class="col-span-2">
                        <div class="flex items-center gap-2">
                            <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight">
                                {{ t('core_my_sub_current') }}</h1>
                            <span wire:click="$set('showModal', true)"
                                class="cursor-pointer pt-[2px] bg-[#ED7A2E] text-white rounded-full font-bold text-[18px] text-center w-[30px] h-[29px] inline-flex justify-center items-center min-w-[30px] min-h-[29px]">
                                ? </span>
                        </div>
                        <x-modal wire:model="showModal" title="{{ t('core_my_sub_current') }}" size="xl">
                            {!! t('core_mon_abonnement_actuel_tooltip') !!}
                        </x-modal>
                    </div>
                    <div class="col-span-4">
                        <h1 class="p-2 text-white bg-primary_ui_high-50 font-bold rounded-lg uppercase">
                            {{ $active->interval->name === 'month' ? t('ui_sub_monthly') : t('ui_sub_yearly') }}
                            {{ t('core_sub_until') }}
                            {{ \Carbon\Carbon::parse($active->ends_at)->format('d F Y') }}
                        </h1>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-5 items-start">
                    <div class="col-span-2 bg-blue-50 rounded-md p-4 flex-start text-center" x-data="{
                        showConfirmModalCancel: false,
                        confirmCancelSub() {
                            this.showConfirmModalCancel = true;
                        },
                        handleConfirmCancel() {
                            this.showConfirmModalCancel = false;
                            $wire.set('loadingElement', true);
                            @this.call('cancelSubscription')
                                .then(() => {
                                    $wire.set('loadingElement', false);
                                    this.showConfirmModalCancel = false;
                                })
                                .finally(() => {
                                    $wire.set('loadingElement', false);
                                });
                            this.showConfirmModalCancel = false;
                        }
                    }">
                        <h2 class="text-black font-bold mb-4"> {{ t('core_sub') }} </h2>

                        <p class="font-semibold text-lg text-black">{{ $active->plan_json['name'] }}</p>
                        <p class="font-medium text-md mt-2 text-black">
                            {{ $active->plan_json['interval']['name'] === 'month' ? t('ui_sub_monthly') : t('ui_sub_yearly') }}
                            payment</p>
                        <p class="font-bold text-2xl mt-1 text-black">
                            {{ money($active->price, $active->currency->code) }}
                        </p>
                        @if ($active->is_canceled_at_end_of_cycle)
                            <p class="text-sm text-red-500 font-semibold mt-8">
                                {{ t('core_sub_end_on') }} <span class="font-bold">{{ $endsAt }}</span>
                            </p>
                        @else
                            <p class="text-sm text-gray-500 font-semibold mt-8">
                                {{ t('core.sub_renewed_on') }} <span class="font-bold">{{ $endsAt }}</span>
                            </p>
                        @endif

                        <!-- <span class="underline cursor-pointer" @click="confirmCancelSub()">Cancel my subscription</span> -->

                        <x-modal-confirm show="showConfirmModalCancel" onConfirm="handleConfirmCancel()">
                            <span>
                                {{ t('core_subscription_change.cancel_sub') }}
                            </span>
                        </x-modal-confirm>
                    </div>

                    <div class="col-span-4 bg-blue-50 rounded-md p-4">
                        <ul class="space-y-3">
                            <li class="flex items-start gap-2">
                                <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                    fill="currentColor" viewBox="0 0 512 512">
                                    <path
                                        d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z" />
                                </svg>
                                <div class="text-lg font-bold text-primary_ui-50">{{ $user_count }}
                                    {{ $user_count === 1 ? t('ui_sub_user') : t('ui_sub_users') }}</div>
                            </li>
                            @foreach ($active->plan_json['product']['features'] as $feature)
                                <li class="flex items-start gap-2">
                                    @if ($feature['pivot']['enabled'])
                                        <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 512 512">
                                            <path
                                                d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z" />
                                        </svg>
                                    @else
                                        <svg class="w-8 h-7 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 512 512">
                                            <path
                                                d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm101.8-262.2L295.6 256l62.2 62.2c4.7 4.7 4.7 12.3 0 17l-22.6 22.6c-4.7 4.7-12.3 4.7-17 0L256 295.6l-62.2 62.2c-4.7 4.7-12.3 4.7-17 0l-22.6-22.6c-4.7-4.7-4.7-12.3 0-17l62.2-62.2-62.2-62.2c-4.7-4.7-4.7-12.3 0-17l22.6-22.6c4.7-4.7 12.3-4.7 17 0l62.2 62.2 62.2-62.2c4.7-4.7 12.3-4.7 17 0l22.6 22.6c4.7 4.7 4.7 12.3 0 17z" />
                                        </svg>
                                    @endif
                                    <div class="text-lg font-bold text-primary_ui-50">
                                        {{ t('ui_sub_feature_' . $feature['key']) }}</div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        @if ($nextSubscription)
            <div class="space-y-6 p-4 border border-primary_ui_high-50">
                <div class="space-y-6 pl-4 pr-4">
                    <div class="grid grid-cols-6">
                        <div class="col-span-2">
                            <div class="flex items-center gap-2">
                                <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight">
                                    {{ t('core.my_next_sub') }}</h1>
                                <span wire:click="$set('showNextSubModal', true)"
                                    class="cursor-pointer pt-[2px] bg-[#ED7A2E] text-white rounded-full font-bold text-[18px] text-center w-[30px] h-[29px] inline-flex justify-center items-center min-w-[30px] min-h-[29px]">
                                    ? </span>
                            </div>
                            <x-modal wire:model="showNextSubModal" title="{{ t('core.my_next_sub') }}" size="xl">
                                {!! t('core.my_next_subscription_tooltip') !!}
                            </x-modal>
                        </div>
                        <div class="col-span-4">
                            <h1 class="p-2 text-white bg-primary_ui_high-50 font-bold rounded-lg uppercase">
                                {{ $nextSubscription->interval->name === 'month' ? t('ui_sub_monthly') : t('ui_sub_yearly') }}
                                {{ t('core_sub_until') }}
                                {{ \Carbon\Carbon::parse($nextSubscription->ends_at)->format('d F Y') }}
                            </h1>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-5 items-start">
                        <div class="col-span-2 bg-blue-50 rounded-md p-4 flex-start text-center"
                            x-data="{

                            }">
                            <h2 class="text-black font-bold mb-4"> {{ t('core_sub') }} </h2>

                            <p class="font-semibold text-lg text-black">{{ $nextSubscription->plan_json['name'] }}</p>
                            <p class="font-medium text-md mt-2 text-black">
                                {{ $nextSubscription->plan_json['interval']['name'] === 'month' ? t('ui_sub_monthly') : t('ui_sub_yearly') }}
                                payment</p>
                            <p class="font-bold text-2xl mt-1 text-black">
                                {{ money($nextSubscription->price, $nextSubscription->currency->code) }}
                            </p>

                            <p class="text-sm  text-primary_ui_high-50 font-semibold mt-8">
                                {{ t('core.sub_starts_on') }} <span
                                    class="font-bold">{{ Carbon::parse($nextSubscription->starts_at)->format('d/m/Y') }}</span>
                            </p>


                        </div>

                        <div class="col-span-4 bg-blue-50 rounded-md p-4">
                            <ul class="space-y-3">
                                <li class="flex items-start gap-2">
                                    <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                        fill="currentColor" viewBox="0 0 512 512">
                                        <path
                                            d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z" />
                                    </svg>
                                    <div class="text-lg font-bold text-primary_ui-50">{{ $user_count }}
                                        {{ $user_count === 1 ? t('ui_sub_user') : t('ui_sub_users') }}</div>
                                </li>
                                @foreach ($nextSubscription->plan_json['product']['features'] as $feature)
                                    <li class="flex items-start gap-2">
                                        @if ($feature['pivot']['enabled'])
                                            <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                                fill="currentColor" viewBox="0 0 512 512">
                                                <path
                                                    d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z" />
                                            </svg>
                                        @else
                                            <svg class="w-8 h-7 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                fill="currentColor" viewBox="0 0 512 512">
                                                <path
                                                    d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm101.8-262.2L295.6 256l62.2 62.2c4.7 4.7 4.7 12.3 0 17l-22.6 22.6c-4.7 4.7-12.3 4.7-17 0L256 295.6l-62.2 62.2c-4.7 4.7-12.3 4.7-17 0l-22.6-22.6c-4.7-4.7-4.7-12.3 0-17l62.2-62.2-62.2-62.2c-4.7-4.7-4.7-12.3 0-17l22.6-22.6c4.7-4.7 12.3-4.7 17 0l62.2 62.2 62.2-62.2c4.7-4.7 12.3-4.7 17 0l22.6 22.6c4.7 4.7 4.7 12.3 0 17z" />
                                            </svg>
                                        @endif
                                        <div class="text-lg font-bold text-primary_ui-50">
                                            {{ t('ui_sub_feature_' . $feature['key']) }}</div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @else
        <div class="space-y-6 p-4 border border-primary_ui_high-50">
            <div class="space-y-6 pl-4 pr-4">
                <div class="grid grid-cols-6">
                    <div class="col-span-2">
                        <div class="flex items-center gap-2">
                            <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight">
                                {{ t('core_my_sub_current') }}</h1>
                            <span
                                class="cursor-pointer pt-[2px] bg-[#ED7A2E] text-white rounded-full font-bold text-[18px] text-center w-[30px] h-[29px] inline-flex justify-center items-center min-w-[30px] min-h-[29px]">
                                ? </span>
                        </div>
                    </div>
                    <div class="col-span-4">
                        <h1 class="p-2 text-white bg-primary_ui_high-50 font-bold rounded-lg uppercase">
                            {{ t('core_sub_no_active') }}
                        </h1>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-5 items-start">
                    <div class="col-span-2 bg-blue-50 rounded-md p-4 flex-start text-center">
                        <h2 class="text-black font-bold mb-4"> {{ t('core_sub') }} </h2>

                        <p class="font-semibold text-lg uppercase text-black"> {{ t('core_sub_free') }} </p>
                        <p class="font-bold text-2xl mt-3 text-black">
                            {{ t('core_sub_permanent') }}
                        </p>
                        <a href="#changesubscription">
                            <button
                                class="mt-[30px] mb-[25px] px-[40px] py-[5px] text-[15px] bg-[#0065a2] hover:bg-[#fed400] text-white border-0 rounded-none font-bold uppercase cursor-pointer">
                                <span data-ts="pvgis.upgrade_now">
                                    {{ t('core_sub_upgrade_now') }}
                                </span>
                            </button>
                        </a>
                    </div>

                    <div class="col-span-4 bg-blue-50 rounded-md p-4">
                        <ul class="space-y-3">
                            <li class="flex items-start gap-2">
                                <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                    fill="currentColor" viewBox="0 0 512 512">
                                    <path
                                        d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z" />
                                </svg>
                                <div class="text-lg font-bold text-primary_ui-50">1 {{ t('ui_sub_user') }}</div>
                            </li>
                            @foreach ($freeProduct as $free)
                                <li class="flex items-start gap-2">
                                    @if ($free['enabled'])
                                        <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 512 512">
                                            <path
                                                d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z" />
                                        </svg>
                                    @else
                                        <svg class="w-8 h-7 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 512 512">
                                            <path
                                                d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm101.8-262.2L295.6 256l62.2 62.2c4.7 4.7 4.7 12.3 0 17l-22.6 22.6c-4.7 4.7-12.3 4.7-17 0L256 295.6l-62.2 62.2c-4.7 4.7-12.3 4.7-17 0l-22.6-22.6c-4.7-4.7-4.7-12.3 0-17l62.2-62.2-62.2-62.2c-4.7-4.7-4.7-12.3 0-17l22.6-22.6c4.7-4.7 12.3-4.7 17 0l62.2 62.2 62.2-62.2c4.7-4.7 12.3-4.7 17 0l22.6 22.6c4.7 4.7 4.7 12.3 0 17z" />
                                        </svg>
                                    @endif
                                    <div class="text-lg font-bold text-primary_ui-50">
                                        {{ t('ui_sub_feature_' . $free['key']) }}</div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="mt-2 space-y-6 p-4 border border-primary_ui_high-50" id="changesubscription">
        <div class="space-y-6 pl-4 pr-4">
            <div class="grid grid-cols-1">
                <div class="col-span-1">
                    <div class=" gap-2">
                        <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight mb-2">
                            {{ t('core_sub_add_new_sub') }}</h1>
                        <h2 class="text-lg font-bold italic mb-1 text-black">
                            {{ t('core_my_sub_switch_subscription_head') }} </h2>
                        <p class="text-gray-500 italic font-bold"> {{ t('core_my_sub_switch_subscription_content') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class=" pl-4 pr-4 subscription-options-container" x-data="{
            isPro: false,
            isYearly: false,
            showConfirmModal: false,
            selectedProduct: null,
            confirmProduct(name, slug, price) {
                this.selectedProduct = { name, slug, price };
                this.showConfirmModal = true;
            },
            handleConfirm() {
                if (this.selectedProduct) {
                    window.location.href = '{{ $checkoutUrl }}' + this.selectedProduct.slug + '?step=1';
                }
                this.showConfirmModal = false;
            }
        }" x-init="$watch('isPro', val => { if (val) isYearly = false })"  >

            @if($nextSubscription)
            {{-- Disabling the subscription options if a future subscription has been scheduled --}}
            <div class="subscription-options-disabler"  ></div>
            @endif
            <div class="flex items-center justify-between mt-4 mb-4">
                <label class="inline-flex items-center cursor-pointer">
                    <span class="font-bold me-3 text-sm text-black">{{ t('ui_sub_none_pro') }}</span>
                    <input type="checkbox" class="sr-only peer" x-model="isPro">
                    <div
                        class="relative w-14 h-8 bg-yellow_custom-50 rounded-full
                                peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300
                                peer-checked:after:translate-x-full
                                rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white
                                after:content-[''] after:absolute after:top-[3.5px] after:start-[3px]
                                after:bg-white after:border-gray-300
                                after:border after:rounded-full after:h-6 after:w-6 after:transition-all">
                        <!-- <div class="absolute top-[1.7px] start-[1.2px] w-7 h-7 rounded-full border-2 border-black
                                    transition-all transform peer-checked:translate-x-full"></div> -->
                    </div>
                    <span class="font-bold ms-3 text-sm text-black">{{ t('ui_sub_pro') }}</span>

                </label>

                <div class="transition-opacity duration-300">
                    <label class="inline-flex items-center cursor-pointer">
                        <span class="font-bold me-3 text-sm text-black">{{ t('ui_sub_monthly') }}</span>
                        <input type="checkbox" class="sr-only peer" x-model="isYearly">
                        <div
                            class="relative w-14 h-8 bg-primary_ui-50 rounded-full
                                peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300
                                peer-checked:after:translate-x-full
                                rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white
                                after:content-[''] after:absolute after:top-[3.5px] after:start-[3px]
                                after:bg-white after:border-gray-300
                                after:border after:rounded-full after:h-6 after:w-6 after:transition-alll">
                        </div>
                        <span class="font-bold ms-3 text-sm text-black">{{ t('ui_sub_yearly') }}</span>
                    </label>
                </div>
            </div>

            <div x-show="isPro" class="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
                @foreach ($planList['productsProfessional'] as $product)
                    @php
                        $isActivePr = ($active->plan_js['slug'] ?? null) === $product['slug'];
                    @endphp
                    <template x-if="isYearly && '{{ $product['type'] }}' === 'Yearly'">
                        <div class="product-card h-full">
                            <div @if (!$isActivePr) @click="confirmProduct('{{ $product['name'] }}', '{{ $product['slug'] }}' , '{{ $product['price'] }}')" @endif
                                class="cursor-pointer">

                                <x-subscription.myplan-card :current="$isActivePr" :product="$product" />
                            </div>
                        </div>
                    </template>
                    <template x-if="!isYearly && '{{ $product['type'] }}' === 'Monthly'">
                        <div class="product-card h-full">
                            <div @if (!$isActivePr) @click="confirmProduct('{{ $product['name'] }}', '{{ $product['slug'] }}' , '{{ $product['price'] }}')" @endif
                                class="cursor-pointer">
                                <x-subscription.myplan-card :current="$isActivePr" :product="$product" />
                            </div>
                        </div>
                    </template>
                @endforeach
            </div>

            <div x-show="!isPro" class="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
                @foreach ($planList['productsParticular'] as $product)
                    @php
                        $isActivePr = ($active->plan_json['slug'] ?? null) === $product['slug'];
                    @endphp
                    <template x-if="isYearly && '{{ $product['type'] }}' === 'Yearly'">
                        <div class="product-card h-full">
                            <div @if (!$isActivePr) @click="confirmProduct('{{ $product['name'] }}', '{{ $product['slug'] }}' , '{{ $product['price'] }}')" @endif
                                class="cursor-pointer">

                                <x-subscription.myplan-card :current="$isActivePr" :product="$product" />
                            </div>
                        </div>
                    </template>
                    <template x-if="!isYearly && '{{ $product['type'] }}' === 'Monthly'">
                        <div class="product-card h-full">
                            <div @if (!$isActivePr) @click="confirmProduct('{{ $product['name'] }}', '{{ $product['slug'] }}' , '{{ $product['price'] }}')" @endif
                                class="cursor-pointer">
                                <x-subscription.myplan-card :current="$isActivePr" :product="$product" />
                            </div>
                        </div>
                    </template>
                @endforeach
            </div>

            <x-modal-confirm show="showConfirmModal" onConfirm="handleConfirm()">
                <span x-show="selectedProduct">
                    {{ t('core_subscription_change.confirmation_to') }}
                    <br>
                    <b x-text="selectedProduct.name"></b>
                </span>
            </x-modal-confirm>

        </div>
    </div>
    <div class="mt-2 space-y-6 p-4 border border-primary_ui_high-50">
        <div class="space-y-6 pl-4 pr-4">
            <div class="grid grid-cols-5">
                <div class="col-span-3">
                    <div class=" gap-2">
                        <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight mb-2">
                            {{ t('core_ui_my_sub_additional') }}</h1>
                        <p class="text-gray-500 italic font-bold"> {!! t('core_ui_my_sub_additional_desc') !!} </p>
                    </div>
                </div>
                <div x-data="{
                    showConfirmModalAdditional: false,
                    confirmAdditionModal() {
                        if (this.showConfirmModalAdditional) return;
                        this.showConfirmModalAdditional = true;
                    },
                    handleConfirmAdditional() {
                        window.location.href = '/checkout/plan/additional-credit?step=1';
                        this.showConfirmModalAdditional = false;
                    }
                }" class="col-span-2">
                    <label @click="confirmAdditionModal()"
                        class="mt-3 flex items-start space-x-3 cursor-pointer hover:scale-[1.5] transition-all duration-300">
                        <span class="w-6 h-6 rounded-full border border-blue-700 bg-white inline-block relative">
                        </span>
                        <span class="font-bold leading-tigh  text-black">
                            <span>{{ t('core_subscription_credit_title') }}</span><br>
                            @money(1000, 'eur') <br>
                            10 {{ t('core_subscription_credit_detail') }}
                        </span>
                    </label>

                    <x-modal-confirm show="showConfirmModalAdditional" onConfirm="handleConfirmAdditional()">
                        <span>
                            {{ t('core_subscription_add_credit') }} <b>ADDITIONAL CREDIT ?</b>
                        </span>
                    </x-modal-confirm>
                </div>
            </div>
        </div>
    </div>

    <div id="payment-credentials-edition" x-data="{
        showConfirmModalCard: false,
        selectedCardId: @entangle('selectedCardId'),
        confirmCard(name, detail, id) {
            this.selectedCard = { name, detail, id };
            this.showConfirmModalCard = true;
        },
        selectedCard: null,
        handleConfirmCard() {
            this.showConfirmModalCard = false;
            $wire.set('loadingElement', true);
            @this.call('confirmCardChange', this.selectedCard.id)
                .then(() => {
                    this.selectedCardId = this.selectedCard.id;
                    this.showConfirmModalCard = false;
                })
                .finally(() => {
                    $wire.set('loadingElement', false);
                    this.selectedCard = null;
                    this.showConfirmModalCard = false;
                });
        }
    }">
        <div class="mt-2 space-y-6 p-4 border border-primary_ui_high-50">
            <div class="space-y-6 pl-4 pr-4">
                <div class="flex items-center justify-between mb-4">
                    <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight">
                        {{ t('core_ui_my_sub_my_current') }}</h1>
                    <a href="/payment-methods"
                        class="text-primary_ui-50 text-xl underline">{{ t('core_ui_my_sub_add') }}</a>
                </div>
                @foreach ($cards as $card)
                    @php
                        $isSelected = $selectedCardId == $card['id'];
                        $isValid = $card['is_valid'];
                    @endphp
                    <label
                        @if (!$isSelected && $isValid) @click="confirmCard('xxxx xxxx xxxx {{ $card['last4'] }}', '{{ $card['exp_month'] }}/{{ $card['exp_year'] }}', '{{ $card['id'] }}')" @endif
                        class="mt-3 flex items-start cursor-pointer  text-black">
                        <span class="w-6 h-6 rounded-full border inline-block relative mt-0"
                            :class="{
                                'border-white bg-primary_ui_high-50': selectedCardId === '{{ $card['id'] }}',
                                'border-primary_ui_high-50 bg-white': selectedCardId !== '{{ $card['id'] }}' &&
                                    {{ $card['is_valid'] ? 'true' : 'false' }},
                                'border-primary_ui_high-50 bg-gray-50': {{ $card['is_valid'] ? 'false' : 'true' }}
                            }"></span>
                        <span class="font-bold leading-tight ml-2 mt-1">
                            {{ t('core_ui_my_sub_credit_card') }} xxxx xxxx xxxx {{ $card['last4'] }} (exp:
                            {{ $card['exp_month'] }}/{{ $card['exp_year'] }})
                        </span>
                    </label>
                @endforeach
            </div>
        </div>
        <x-modal-confirm show="showConfirmModalCard" onConfirm="handleConfirmCard()">
            <span show="selectedCard && ">
                {{ t('core_subscription_change.confirmation_card') }}
                <br>
                <b show="selectedCard.name" x-text="selectedCard.name"></b> - <b show="selectedCard.detail"
                    x-text="selectedCard.detail"></b>
            </span>
        </x-modal-confirm>
    </div>

    <div class="mt-2 space-y-6 p-4 border border-primary_ui_high-50 mb-3">
        <div class="space-y-6 pl-4 pr-4">
            <div class="overflow-x-auto">
                <table class="w-full text-left border-collapse">
                    <thead class="mb-2">
                        <tr class="border-0">
                            <th class="py-2 font-bold text-xl text-black text-primary_ui_high-50">
                                {{ t('ui_sub_invoice_my') }} </th>
                            <th class="py-2 font-bold text-xl text-black"> {{ t('ui_sub_invoice_date') }}</th>
                            <th class="py-2 font-bold text-xl text-black"> {{ t('ui_sub_invoice_sub') }}</th>
                            <th class="py-2 font-bold text-xl text-black"> {{ t('ui_sub_invoice_amount') }}</th>
                            <th class="py-2 font-bold text-xl text-black"> {{ t('ui_sub_invoice_status') }}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-600">
                        @foreach ($transactionUser as $transaction)
                            <tr class="border-b hover:bg-gray-50">
                                <td class="py-3 text-black text-lg font-bold">
                                    {{ \Carbon\Carbon::parse($transaction->created_at)->format('Y-m') }}-{{ $transaction->id }}
                                </td>
                                <td class="py-3 text-black text-lg font-bold">
                                    {{ \Carbon\Carbon::parse($transaction->created_at)->format('d/m/Y') }}
                                </td>
                                <td class="py-3 text-black text-lg font-bold">
                                    {{ $transaction->plan ? $transaction->plan->name : '-' }}
                                </td>
                                <td class="py-3 text-black text-lg font-bold">
                                    @money($transaction->amount, 'eur')
                                </td>
                                <td class="py-3 text-black text-lg font-bold">
                                    <span
                                        class="{{ $transaction->payment_provider_status == 'paid' ? 'text-green-500' : 'text-red-500' }} uppercase">
                                        {{ t('ui_sub_state_payement_' . $transaction->payment_provider_status) }}

                                    </span>
                                </td>
                                <td class="py-3">
                                    <a href="{{route('invoice.generate', ['transactionUuid' => $transaction->uuid])}}" class="text-primary_ui-50 underline text-black">
                                        {{ t('ui_sub_invoice_download') }}</a> <br>
                                    {{ t('ui_sub_invoice_my_receips') }}: <br><a href="{{route('receipt.generate', ['transactionUuid' => $transaction->uuid])}}"
                                        class="text-primary_ui-50 underline">2025-06-0294</a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div x-data="{ show: @entangle('loadingElement') }">
        <x-ui.loading-overlay x-show="show" />
    </div>
</x-filament::page>
