<x-layouts.extranet>
    <x-slot name="content">
        <div class="flex items-center py-2 md:py-10 gap-4 h-full w-full justify-center">
            <div class="w-full md:max-w-xl bg-base-100 shadow-regular p-4 md:p-8">
                @if (!empty($registered))
                    <div class="text-center space-y-2 md:space-y-4 py-2">
                        <p class="font-bold">{{ __('Un mail de validation vient de vous être envoyé à :') }}</p>
                        <p class="italic">{{ $userMail ?? '-' }}</p>
                        <p class="font-bold">{{ __('Merci de cliquer sur le lien pour confirmer votre inscription.') }}</p>
                        <a
                            href="{{ route('login') }}"
                            class="btn text-white bg-primary_ui-50 hover:bg-primary_ui-50 md:!mt-8 !mt-6"
                        >
                            {{ __('Go to Login') }}
                        </a>
                    </div>
                @else
                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        <div class="flex justify-between">
                            <h1 class="text-start font-bold text-xl">{{ __('app.auth.register') }}</h1>
                            <p class="text-sm mt-2 text-end">{{__('app.auth.have_account')}} <a class="text-primary-950 font-bold underline" href="{{ route('login') }}">{{__('app.auth.login')}}</a></p>
                        </div>

                        <x-input.field label="{{ __('app.auth.name') }}" type="text" name="name"
                                    value="{{ old('name') }}" autofocus="true" class="my-2"
                                    autocomplete="name" max-width="w-full"/>

                        @error('name')
                        <span class="text-sm text-red-500" role="alert">
                                {{ $message }}
                            </span>
                        @enderror

                        <x-input.field label="{{ __('app.auth.email') }}" type="email" name="email"
                                    value="{{ old('email') }}" class="my-2"
                                    autocomplete="email" max-width="w-full"/>
                        @error('email')
                        <span class="text-sm text-red-500" role="alert">
                                {{ $message }}
                            </span>
                        @enderror

                        <x-input.field label="{{ __('app.auth.password') }}" type="password" name="password" class="my-2"  max-width="w-full"/>

                        @error('password')
                        <span class="text-sm text-red-500" role="alert">
                                {{ $message }}
                            </span>
                        @enderror

                        <x-input.field label="{{ __('app.auth.confirm_password') }}" type="password" name="password_confirmation" class="my-2"  max-width="w-full"/>

                        @error('password')
                        <span class="text-sm text-red-500" role="alert">
                                {{ $message }}
                            </span>
                        @enderror

                        @if (config('app.recaptcha_enabled'))
                            <div class="my-4">
                                {!! htmlFormSnippet() !!}
                            </div>

                            @error('g-recaptcha-response')
                            <span class="text-sm text-red-500" role="alert">
                                    {{ $message }}
                                </span>
                            @enderror

                            @push('tail')
                                {!! htmlScriptTagJsApi() !!}
                            @endpush
                        @endif

                        <div class="flex flex-wrap gap-8 justify-end items-center">
                            <div>
                                <a href="{{config('app.url')}}" class="text-primary-950">{{ __('app.auth.cancel') }}</a>
                            </div>
                            <div class="basis-1/2">
                                <x-button-link.primary-ui class="inline-block !w-full my-2" elementType="button" type="submit">
                                    {{ __('app.auth.register') }}
                                </x-button-link.primary-ui>
                            </div>
                        </div>

                        <!-- <x-auth.social-login>
                            <x-slot name="before">
                                <div class="flex flex-col w-full">
                                    <div class="divider">{{ __('or') }}</div>
                                </div>
                            </x-slot>
                        </x-auth.social-login> -->

                    </form>
                @endif
            </div>
        </div>
    </x-slot>

</x-layouts.extranet>
