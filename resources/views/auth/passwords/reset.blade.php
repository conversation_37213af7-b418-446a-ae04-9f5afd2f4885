<x-layouts.extranet>
    <x-slot name="content">
        <div class="flex items-center py-2 md:py-10 gap-4 h-full w-full justify-center">
            <div class="w-full md:max-w-xl bg-base-100 shadow-regular p-4 md:p-8">

                <form method="POST" action="{{ route('password.update') }}">
                    @csrf
                    <h1 class="text-start font-bold text-xl">{{ __('app.auth.reset_password') }}</h1>

                    <input type="hidden" name="token" value="{{ $token }}">

                    <x-input.field label="{{ __('app.auth.email') }}" type="email" name="email"
                                   value="{{ $email ?? old('email') }}" required autofocus="true" class="my-2"
                                   autocomplete="email" max-width="w-full"/>

                    @error('email')
                        <span class="text-xs text-red-500" role="alert">
                            {{ $message }}
                        </span>
                    @enderror

                    <x-input.field label="{{ __('app.auth.password') }}" type="password" name="password" required class="my-2"  max-width="w-full"/>

                    @error('password')
                        <span class="text-xs text-red-500" role="alert">
                            {{ $message }}
                        </span>
                    @enderror

                    <x-input.field label="{{ __('app.auth.confirm_password') }}" type="password" name="password_confirmation" required class="my-2"  max-width="w-full"/>

                    @error('password')
                    <span class="text-xs text-red-500" role="alert">
                            {{ $message }}
                        </span>
                    @enderror

                    <div class="flex flex-wrap gap-8 justify-end items-center">
                        <div>
                            <a href="/" class="text-primary-950">{{ __('app.auth.cancel') }}</a>
                        </div>
                        <div class="basis-1/2">
                            <x-button-link.primary-ui class="inline-block !w-full my-2" elementType="button" type="submit">
                                {{ __('app.auth.reset_password_btn') }}
                            </x-button-link.primary-ui>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </x-slot>

</x-layouts.extranet>
