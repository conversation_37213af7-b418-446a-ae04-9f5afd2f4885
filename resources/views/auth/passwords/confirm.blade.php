<x-layouts.focus>
    <x-slot name="left">
        <div class="flex flex-col py-2 md:py-10 gap-4 h-full">
            <div class="">
                <h1 class="text-start font-bold text-xl">{{ __('app.auth.confirm_password') }}</h1>
            </div>
            <div class="card w-full md:max-w-xl bg-base-100 shadow-xl p-4 md:p-8">

                <form method="POST" action="{{ route('password.confirm') }}">
                    @csrf

                    <x-input.field label="{{ __('app.auth.password') }}" type="password" name="password" required class="my-2"  max-width="w-full" autocomplete="current-password"/>

                    @error('password')
                    <span class="text-xs text-red-500" role="alert">
                            {{ $message }}
                        </span>
                    @enderror

                    <div class="my-3 flex flex-wrap gap-2 justify-between text-sm">
                        <div>
                            @if (Route::has('password.request'))
                                <a class="text-primary-950 text-xs" href="{{ route('password.request') }}">
                                    {{ __('app.auth.forgot_password') }}
                                </a>
                            @endif
                        </div>
                    </div>

                    <div class="flex flex-wrap gap-8 justify-end items-center">
                        <div>
                            <a href="/" class="text-primary-950 ">{{ __('app.auth.cancel') }}</a>
                        </div>
                        <div class="basis-1/2">
                            <x-button-link.primary-ui class="inline-block !w-full my-2" elementType="button" type="submit">
                                {{ __('app.auth.confirm_password_btn') }}
                            </x-button-link.primary-ui>
                        </div>
                    </div>

                </form>

            </div>
        </div>
    </x-slot>

</x-layouts.focus>
