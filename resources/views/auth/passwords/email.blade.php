<x-layouts.extranet>
    <x-slot name="content">
        <div class="flex items-center py-2 md:py-10 gap-4 h-full w-full justify-center">
            <div class="w-full md:max-w-xl bg-base-100 shadow-regular p-4 md:p-8">

                @if (session('status'))
                    <div role="alert" class="alert my-4 text-sm">
                        {{ session('status') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('password.email') }}">
                    @csrf
                    <h1 class="text-start font-bold text-xl">{{ __('app.auth.forgot_password') }}</h1>
                    <x-input.field label="{{ __('app.auth.email') }}" type="email" name="email"
                                   value="{{ old('email') }}" required autofocus="true" class="my-2"
                                   autocomplete="email" max-width="w-full"/>

                    @error('email')
                        <span class="text-xs text-red-500" role="alert">
                            {{ $message }}
                        </span>
                    @enderror

                    @if (config('app.recaptcha_enabled'))
                        <div class="my-4">
                            {!! htmlFormSnippet() !!} <!-- reCAPTCHA widget -->
                        </div>

                        @error('g-recaptcha-response')
                            <span class="text-xs text-red-500" role="alert">
                                {{ $message }}
                            </span>
                        @enderror
                    @endif

                    <div class="flex flex-wrap gap-8 justify-end items-center">
                        <div>
                            <a href="/login" class="text-primary-950 ">{{ __('app.auth.cancel') }}</a>
                        </div>
                        <div class="basis-1/2">
                            <x-button-link.primary-ui class="inline-block !w-full my-2" elementType="button" type="submit">
                                {{ __('Receive E-mail') }}
                            </x-button-link.primary-ui>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </x-slot>

    @if (config('app.recaptcha_enabled'))
        @push('tail')
            {!! htmlScriptTagJsApi() !!} <!-- Include reCAPTCHA script -->
        @endpush
    @endif

</x-layouts.extranet>
