@php
    $id = 'monaco-editor-' . uniqid();
@endphp
@vite(['resources/css/fonts.css', 'resources/css/app.css', 'resources/js/app.js'])

<div>
    <input type="hidden" id="{{ $id }}-input" name="{{ $getName() }}" value="{{ $getState() }}">
    <label for="{{ $id }}" class="text-sm font-medium leading-6 text-gray-950 dark:text-white">
        {{ $getLabel() }}@if ($isRequired())<sup class="text-danger-600 dark:text-danger-400 font-medium">*</sup> @endif
    </label>
    <div id="{{ $id }}" style="height: {{ $getHeight() }}; border: 1px solid #ccc;"></div>
</div>

@push('scripts')
    <script>
        function waitForMonacoAndInit(callback, attempts = 10) {
            if (window.monaco && window.monaco.editor) {
                callback();
            } else if (attempts > 0) {
                setTimeout(() => waitForMonacoAndInit(callback, attempts - 1), 200);
            } else {
                console.error('Monaco Editor could not be loaded.');
            }
        }

        var container = document.getElementById('{{ $id }}');
        var hiddenInput = document.getElementById('{{ $id }}-input');
        if (container && !container.dataset.initialized) {
            container.dataset.initialized = 'true';
            waitForMonacoAndInit(() => {
                const editor = window.monaco.editor.create(container, {
                    value: hiddenInput.value,
                    language: '{{ $getLanguage() }}',
                    theme: '{{ $getTheme() }}',
                    automaticLayout: true,
                });
                editor.onDidChangeModelContent(() => {
                    hiddenInput.value = editor.getValue();
                    console.log('Monaco change:', editor.getValue(), hiddenInput, hiddenInput.value);
                });
            });
        }
    </script>
@endpush
