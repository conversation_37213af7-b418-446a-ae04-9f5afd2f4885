<div
    class="bg-gray-100 p-5 space-y-4"
>
    <h3 class="text-lg font-semibold text-red-600">
        {{ __('Delete Account') }}
    </h3>

    <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ __('Once your account is deleted, all of its resources and data will be permanently deleted.') }}
    </p>

    <x-filament::button
        color="danger" 
        wire:click="openConfirmationModal"
        icon="heroicon-o-trash"
        class="danger-btn"
    >
        {{ __('Delete Account') }}
    </x-filament::button>

    <x-filament::modal
        id="confirm-delete-account-modal"
        icon="heroicon-o-exclamation-triangle"
        icon-color="danger"
        :wire-close="false"
        :close-by-clicking-away="true"
        width="lg"
    >
        <x-slot name="heading">
            {{ __('Are you absolutely sure?') }}
        </x-slot>

        <x-slot name="description">
            <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ __('This will permanently delete your account and all associated data. This action cannot be undone.') }}
            </p>
        </x-slot>

        <x-slot name="footerActions">
            <div class="w-full flex justify-end space-x-2">
                <x-filament::button
                    color="gray"
                    wire:click="closeConfirmationModal"
                    >
                    {{ __('Cancel') }}
                </x-filament::button>
                <x-filament::button color="danger" wire:click="deleteAccount">
                    {{ __('Yes, delete my account') }}
                </x-filament::button>
            </div>
        </x-slot>
    </x-filament::modal>
</div>