<div
  x-data="{
    viewMode: 'list',
    filter: 'all',
    preferences: {{ json_encode($preferences) }}, 
    isLoading: false,
    isSuccess: false,

    changeFilter(newFilter) {
      this.filter = newFilter;
      this.viewMode = 'list'; 
      this.$wire.setFilter(newFilter);

    },
    changeViewMode(newMode) {
      this.viewMode = newMode;
    },
    savePreferences() {
      this.isLoading = true;
      this.isSuccess = false;
      this.$wire.savePreferences(this.preferences)
        .then(() => {
          this.isSuccess = true;
        })
        .catch(error => {
          console.error('An error occurred:', error);
          alert('Could not save settings. Please try again.');
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }"
  wire:poll.5s="updateUnreadCount"
  class="p-4 bg-white border-[1px] rounded-lg shadow-md fi-section dark:bg-gray-800 border-primary_ui_high-900 fi-section"
>
  <div
    class="flex items-center justify-between gap-4 pb-4 border-b dark:border-gray-700"
  >
    <div class="flex items-center gap-2">
      <button
        @click="changeFilter('all')"
        :class="{ 'bg-gray-200': viewMode === 'list' && filter === 'all', '': !(viewMode === 'list' && filter === 'all') }"
         class="px-3 py-1 rounded-md text-primary_ui_high-900 hover:bg-gray-200"
      >
        All
      </button>
      <button
        @click="changeFilter('unread')"
        :class="{ 'bg-gray-200': viewMode === 'list' && filter === 'unread', '': !(viewMode === 'list' && filter === 'unread') }"
         class="px-3 py-1 rounded-md text-primary_ui_high-900 hover:bg-gray-200 relative"
      >
        Unread
        @if ($unreadCount < 9)
          <span
            class="absolute top-0 right-0 w-4 h-4 text-xs text-white transform translate-x-1/2 -translate-y-1/2 rounded-full bg-danger-600"
            >{{ $unreadCount }}</span
          >
          @else
          <span
            class="absolute top-0 right-0 w-4 h-4 text-xs text-white transform translate-x-1/2 -translate-y-1/2 rounded-full bg-danger-600"
            >9+</span
          >
        @endif
      </button>
      <button
        @click="changeFilter('read')"
        :class="{ 'bg-gray-200': viewMode === 'list' && filter === 'read', ' ': !(viewMode === 'list' && filter === 'read') }"
        class="px-3 py-1 rounded-md text-primary_ui_high-900 hover:bg-gray-200"
      >
        Read
      </button>
    </div>
    <div class="flex gap-4 items-center">
      <div>
        @if ($unreadCount > 0 && $filter !== 'read')
          <div class="flex justify-end py-2">
            <button
              wire:click.prevent="markAllAsRead"
              wire:loading.attr="disabled"
              class="text-sm font-medium text-primary_ui_high-50 hover:text-primary_ui_high-50 dark:text-primary_ui_high-50 dark:hover:text-primary-400"
            >
              Mark all as read
            </button>
          </div>
        @endif
      </div>
      <div>
        <button
          @click="changeViewMode('settings')"
          aria-label="Notification Settings"
        >
          <img src="{{ asset("images/menu/weather-blue.png") }}" width="26"/>
        </button>
      </div>
    </div>
  </div>

  <div class="mt-6">
    <div x-show="viewMode === 'list'" x-cloak>
      <ul
        role="list"
        class="-my-4 divide-y divide-gray-200 dark:divide-gray-700"
      >
        @forelse ($notifications as $notification)
          <li
            wire:key="{{ $notification['id'] }}"
            class="flex items-center py-4 space-x-4"
          >
            <div class="flex-1 min-w-0 space-y-2">
              <h2 class="font-semibold text-primary_ui_high-50 truncate dark:text-primary-200">{{ $notification['data']['title'] ?? 'Title.' }}</h2>
              <p class="text-gray-800 truncate dark:text-gray-200">
                {{ $notification['data']['message'] ?? 'No message content.' }}
              </p>
              <p class="text-xs italic text-gray-500 dark:text-gray-400">
                <span>{{ $notification['created_at']->toDayDateTimeString() }}</span>
              </p>
            </div>
            <div>
              @if (!$notification['read_at'])
                <button
                  wire:click.prevent="markAsRead('{{ $notification['id'] }}')"
                  class="px-3 py-1 text-sm font-medium text-white rounded-lg bg-primary_ui_high-50 hover:bg-primary_ui_high-50"
                >
                  Mark Read
                </button>
              @endif
            </div>
          </li>
        @empty
          <li class="py-10 text-center">
            <p class="text-gray-500 dark:text-gray-400">
              No notifications here. All clear!
            </p>
          </li>
        @endforelse
      </ul>
    </div>

    <div x-show="viewMode === 'settings'" x-cloak>
      <div class="space-y-8">
        @if ($preferenceTypes)
          @foreach ($preferenceTypes as $type => $preferencesOfType)
            <div wire:key="type-{{ \Illuminate\Support\Str::slug($type) }}">
              <h3 class="text-lg font-bold text-gray-900 dark:text-white">
                {{ $type }}
              </h3>
              <div class="mt-4 space-y-4">
                @foreach ($preferencesOfType as $preference)
                  <label
                    wire:key="pref-{{ $preference['key'] }}"
                    class="flex items-center space-x-3"
                  >
                    <input
                      type="checkbox"
                      x-model="preferences['{{ $preference['key'] }}']"
                      :checked="preferences['{{ $preference['key'] }}'] === 1 ? true : false"
                      :disabled="isLoading"
                      class="w-5 h-5 rounded text-primary_ui_high-50 focus:ring-primary_ui_high-50 dark:bg-gray-700 dark:border-gray-600 disabled:text-primary-100 disabled:cursor-not-allowed"
                    />
                    <span class="text-gray-700 dark:text-gray-300">{{
                      $preference['name']
                    }}</span>
                  </label>
                @endforeach
              </div>
            </div>
          @endforeach
        @endif
        <div
          class="flex items-center justify-between pt-6 mt-6 border-t dark:border-gray-700"
        >
          <button
            @click="savePreferences"
            :disabled="isLoading"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary_ui_high-50 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary_ui_high-50 disabled:opacity-50"
          >
            <span x-show="!isLoading">Save Preferences</span>
            <span x-show="isLoading">Saving...</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>