<div 
    class="w-full"
    x-data="{ step: @entangle('currentStep') }"
>

    <div class="w-full bg-primary_ui_high-1900 mb-2 pt-2">
        <div class="container flex justify-end max-w-6xl w-full space-x-9 m-auto p-1">
            <a 
                :href="(() => {
                        const url = new URL(window.location.href);
                        url.searchParams.set('step', 1);
                        return url.pathname + url.search;
                    })()"
                    :class="step === 1 ? 'border-yellow-500 text-yellow-500' : 'border-white text-white'"
                class="flex flex-col items-center">
                <div
                    
                    class="w-12 h-12 flex items-center justify-center cursor-pointer"
                >
                    <img id="cart" width="50" height="50" src="/images/checkout/cart-icon-yellow.png">
                </div>
                <span class="text-sm mt-2 hover:underline text-yellow-400">{{ t('ui_sub_checkout_cart') }}</span>
            </a>

            <a  :href="(() => {
                        const url = new URL(window.location.href);
                        url.searchParams.set('step', 2);
                        return url.pathname + url.search;
                })()"
                :class="step === 2 ? 'border-yellow-500 text-yellow-500' : 'border-white text-white'" 
                class="flex flex-col items-center">
                <div
                   
                    class="w-12 h-12 flex items-center justify-center cursor-pointer"
                >
                <img id="customer_info" width="50" height="50" src="/images/checkout/customer-info-icon-yellow.png">
                </div>
                <span class="text-sm mt-2 hover:underline text-yellow-400">{{ t('ui_sub_checkout_id') }}</span>
            </a>

            <a 
                :href="(() => {
                        const url = new URL(window.location.href);
                        url.searchParams.set('step', 3);
                        return url.pathname + url.search;
                    })()"
                :class="step === 3 ? 'border-yellow-500 text-yellow-500' : 'border-white text-white'"
                class="flex flex-col items-center">
                <div
                    
                    class="w-12 h-12 flex items-center justify-center cursor-pointer"
                >
                    <img id="payment" width="50" height="50" src="/images/checkout/payment-icon-yellow.png">
                </div>
                <span class="text-sm mt-2 hover:underline text-yellow-400">{{ t('ui_sub_checkout_payment') }}</span>
            </a>
        </div>
    </div>

    <div x-show="step === 1">
        <div class="mt-4 mb-2 max-w-4xl mx-auto">
            <div class="flex items-center space-x-3" x-show="step === 1" x-cloak>
                <img width="60" height="60" src="/images/checkout/cart-icon.png">
                <h1 class="ml-3 text-6xl font-bold text-primary_ui-50">{{ t('ui_sub_checkout_cart') }}</h1>
            </div>
        </div>

        <div class="max-w-4xl mx-auto pt-0 p-6 bg-white rounded-lg">
            <form action="" method="post" wire:submit="checkout">
                @csrf
                <div class="flex gap-x-4">
                    @guest
                        {{--    
                        <div class="w-1/2">
                            @include('livewire.checkout.partials.login-or-register')
                        </div> 
                        --}}
                        <div class="w-full">
                            @include('livewire.checkout.partials.plan-details')
                            <div class="hidden">
                                @include('livewire.checkout.partials.payment')
                            </div>
                        </div>
                    @else
                        <div class="w-full">
                            @include('livewire.checkout.partials.plan-details')
                            <div class="hidden">
                                @include('livewire.checkout.partials.payment')
                            </div>
                        </div>
                    @endguest
                </div>
            </form>
        </div>
    </div>

    <div x-show="step === 2">
        @if ($isAdmin)
            <div class="alert">
                <p>You are an admin, you cannot subscribe any offer</p>
            </div>
        @else
            <div class="mt-4 mb-2 max-w-4xl mx-auto">
                <div class="flex items-center space-x-3" x-show="step === 2" x-cloak>
                    <img width="60" height="60" src="/images/checkout/customer-info-icon.png">
                    <h1 class="ml-3 text-6xl font-bold text-primary_ui-50">{{ t('ui_sub_checkout_id') }}</h1>
                </div>
            </div>

            @include('livewire.checkout.steps.identity')
        @endif
    </div>

    <div x-show="step === 3">
        @if ($isAdmin)
            <div class="alert">
                <p>You are an admin, you cannot subscribe any offer</p>
            </div>
        @else
            <div class="mt-4 mb-2 max-w-4xl mx-auto">
                <div class="flex items-center space-x-3" x-show="step === 3" x-cloak>
                    <img width="60" height="60" src="/images/checkout/payment-icon.png">
                    <h1 class="ml-3 text-6xl font-bold text-primary_ui-50">{{ t('ui_sub_checkout_payment') }}</h1>
                </div>
            </div>

            @include('livewire.checkout.steps.payment')
        @endif
    </div>

    <div x-data="{ show: @entangle('loadingElementInCheckout') }">
        <div
            x-show="show"
            x-transition.opacity
            class="fixed inset-0 bg-white/70 z-50 flex items-center justify-center"
            style="display: none;"
        >
            <div class="w-16 h-16 border-4 border-primary_ui_high-50 border-t-transparent rounded-full animate-spin"></div>
        </div>
    </div>
</div>