@php
    $user_count = ($plan) ? $plan->user_count : null;
@endphp

<div class="mt-3 max-w-3xl mx-auto bg-gray-100 shadow rounded-lg overflow-hidden p-5">
    <table class="w-full text-left border-collapse">
        <thead class="bg-gray-200 rounded-lg">
        <tr class="text-primary_ui-50 font-bold rounded-lg">
            <th class="text-primary_ui-50 text-xl py-5 px-4">{{ t('ui_checkout_subscription') }}</th>
            <th class="text-primary_ui-50 text-xl py-5 px-4">{{ t('ui_checkout_unit_price') }}</th>
            <th class="text-primary_ui-50 text-xl py-5 px-4">{{ t('ui_checkout_qt') }}</th>
            <th class="text-primary_ui-50 text-xl py-5 px-4">{{ t('ui_checkout_totalprice') }}</th>
            <th class="text-primary_ui-50 text-xl py-5 px-4"></th>
        </tr>
        </thead>

        <tbody>
        <tr class="border-0">
            <td class="py-4 px-4">
                @if ($plan->slug == 'additional-credit')
                    <span class="text-lg bg-primary_ui_high-1900 text-white py-1 text-center rounded block">
                        {{ t('ui_checkout_add_credit') }}
                    </span>
                @endif
                <strong class="font-bold text-xl block mt-1 text-black">
                    {{ $plan->product->name }}
                </strong>
                @if ($plan->slug != 'additional-credit')
                    <p class="text-lg block mt-1 text-black">
                        {{ $plan->product->account_type }}
                    </p>
                    <p class="text-lg block mt-1 text-black">
                        {{ mb_convert_case($plan->interval->adverb, MB_CASE_TITLE, 'UTF-8') }} {{ t('ui_sub_checkout_subscription') }}
                    </p>
                @endif
            </td>

            <td class="py-4 px-4 text-lg font-bold">@money($priceValue, "eur")</td>

            <td class="py-4 px-4 text-center text-lg font-bold">1</td>

            <td class="py-4 px-4 font-semibold text-lg font-bold">@money($priceValue, "eur")</td>

            <td class="py-4 px-4 text-center">
            <button class="text-gray-400 hover:text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            </button>
            </td>
        </tr>
        <tr class="border-0">
            <td class="">
                @if ($plan->slug != 'additional-credit')
                    <ul class="space-y-3">
                        @if ($plan->product->features)
                            <li class="flex items-start gap-2">
                                <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 512 512">
                                    <path d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z"/>
                                </svg>
                                <div class="text-lg font-bold text-primary_ui-50">{{ $user_count }} {{ $user_count === 1 ? t('ui_sub_user') : t('ui_sub_users') }}</div>
                            </li>
                            @foreach($plan->product->features as $feature)
                                <li class="flex items-start gap-2">
                                    @if ($feature['pivot']['enabled'])
                                    <svg class="w-8 h-7 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 512 512">
                                        <path d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm0 48c110.532 0 200 89.451 200 200 0 110.532-89.451 200-200 200-110.532 0-200-89.451-200-200 0-110.532 89.451-200 200-200m140.204 130.267l-22.536-22.718c-4.667-4.705-12.265-4.736-16.97-.068L215.346 303.697l-59.792-60.277c-4.667-4.705-12.265-4.736-16.97-.069l-22.719 22.536c-4.705 4.667-4.736 12.265-.068 16.971l90.781 91.516c4.667 4.705 12.265 4.736 16.97.068l172.589-171.204c4.704-4.668 4.734-12.266.067-16.971z"/>
                                    </svg>
                                    @else
                                    <svg class="w-8 h-7 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 512 512">
                                        <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm101.8-262.2L295.6 256l62.2 62.2c4.7 4.7 4.7 12.3 0 17l-22.6 22.6c-4.7 4.7-12.3 4.7-17 0L256 295.6l-62.2 62.2c-4.7 4.7-12.3 4.7-17 0l-22.6-22.6c-4.7-4.7-4.7-12.3 0-17l62.2-62.2-62.2-62.2c-4.7-4.7-4.7-12.3 0-17l22.6-22.6c4.7-4.7 12.3-4.7 17 0l62.2 62.2 62.2-62.2c4.7-4.7 12.3-4.7 17 0l22.6 22.6c4.7 4.7 4.7 12.3 0 17z"/>
                                    </svg>
                                    @endif
                                    <div class="text-lg font-bold text-primary_ui-50">{{ t('ui_sub_feature_'.$feature['key']) }}</div>
                                </li>
                            @endforeach
                        @endif
                    </ul>
                @endif
            </td>
            <td colspan="5"></td>
        </tr>
        </tbody>
    </table>

    @if(false):
    <div class="mt-4 w-full flex justify-end items-center text-xl font-bold bg-gray-200 rounded-lg p-4">
        <span class="mr-6 text-primary_ui-50">Remboursement (€99-€9)</span>
        <span class="text-black">€90</span>
    </div>
    @endif

    <div class="mt-4 w-full flex justify-end items-center text-xl font-bold bg-gray-200 rounded-lg p-4">
        <span class="mr-6 text-primary_ui-50">TOTAL</span>
        <span class="text-black"> €{{ $totals->amountDue / 100 }} </span>
    </div>


    <div class="mt-4 flex justify-end">
        <a
        :href="(() => {
            const url = new URL(window.location.href);
            url.searchParams.set('step', 2);
            return url.pathname + url.search;
        })()"
        class="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded transition">
        {{ t('ui_checkout_validate') }}
        </a>
    </div>
</div>
