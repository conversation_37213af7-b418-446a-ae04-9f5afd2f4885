<div>
    <div class="flex flex-col py-2 md:py-10 gap-4 h-full">
        
        <div class="card w-full md:max-w-xl bg-base-100 shadow-xl p-4 md:p-8">
            <form method="POST" action="{{ route('login') }}">
                @csrf

                <p class="text-sm mt-2 text-end">
                    {{ __('app.auth.no_account') }}
                    <a class="text-primary-950 font-bold underline" href="{{ route('register') }}">
                        {{ __('app.auth.register') }}
                    </a>
                </p>

                <x-input.field label="{{ __('app.auth.email') }}" type="email" name="email"
                               value="{{ old('email') }}" autofocus="true" class="my-2"
                               autocomplete="email" max-width="w-full"/>
                @error('email')
                    <span class="text-sm text-red-500" role="alert">{{ $message }}</span>
                @enderror

                <x-input.field label="{{ __('app.auth.password') }}" type="password" name="password"
                               class="my-2" max-width="w-full"/>
                <input type="hidden" name="redirect-step-2" x-data x-init="$el.value = window.location.pathname">

                @error('password')
                    <span class="text-sm text-red-500" role="alert">{{ $message }}</span>
                @enderror

                @if (config('app.recaptcha_enabled'))
                    <div class="my-4">
                        {!! htmlFormSnippet() !!}
                    </div>
                    @error('g-recaptcha-response')
                        <span class="text-sm text-red-500" role="alert">{{ $message }}</span>
                    @enderror
                    @push('tail')
                        {!! htmlScriptTagJsApi() !!}
                    @endpush
                @endif

                <div class="my-3 flex flex-wrap gap-2 justify-between text-sm">
                    <div class="flex gap-2">
                        <input class="checkbox checkbox-sm" type="checkbox" name="remember"
                               id="remember" {{ old('remember') ? 'checked' : '' }}>
                        <label class="text-sm" for="remember">
                            {{ __('app.auth.remember_me') }}
                        </label>
                    </div>
                    <div>
                        @if (Route::has('password.request'))
                            <a class="text-primary-950 text-sm underline italic" href="{{ route('password.request') }}">
                                {{ __('app.auth.forgot_password') }}
                            </a>
                        @endif
                    </div>
                </div>

                <div class="flex flex-wrap gap-8 justify-end items-center">
                    <div>
                        <a href="/" class="text-primary-950 ">{{ __('app.auth.cancel') }}</a>
                    </div>
                    <div class="basis-1/2">
                        <x-button-link.primary class="inline-block !w-full my-2" elementType="button" type="submit">
                            {{ __('app.navigation.login') }}
                        </x-button-link.primary>
                    </div>
                </div>

                {{-- Uncomment if social login is enabled --}}
                {{-- 
                <x-auth.social-login>
                    <x-slot name="before">
                        <div class="flex flex-col w-full">
                            <div class="divider">{{ __('or') }}</div>
                        </div>
                    </x-slot>
                </x-auth.social-login>
                --}}
            </form>
        </div>
    </div>
</div>
