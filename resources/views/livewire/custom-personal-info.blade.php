{{-- <x-filament-breezy::grid-section md=1 title="{{ __('Informations personnelles') }}" description="{{__('Gérer vos informations personnelles.')}}"> --}}
{{-- <x-filament-breezy::grid-section title="{{ __('dsfa') }}" description="{{__('')}}"> --}}
<div>
    <div class="bg-gray-100 p-5">
        <form wire:submit.prevent="submit" class="space-y-6">
            {{ $this->form }}
            <div class="text-right">
                <x-filament::button type="submit" form="submit" class="align-right">
                    {{ __('Save') }}
                </x-filament::button>
            </div>
        </form>
    </div>        
</div>        
{{-- </x-filament-breezy::grid-section> --}}

{{-- 
<div class="max-w-3xl mx-auto space-y-6">
    <form wire:submit.prevent="save" class="space-y-6">
        <!-- Avatar / Logo -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium mb-1">{{ __('Photo') }}</label>
                <input type="file" wire:model="avatar" class="block w-full text-sm">
                @error('avatar') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label class="block text-sm font-medium mb-1">{{ __('Company logo') }}</label>
                <input type="file" wire:model="company_logo" class="block w-full text-sm">
                @error('company_logo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>
        </div>

        <!-- Name -->
        <div>
            <label class="block text-sm font-medium mb-1">{{ __('Name') }}</label>
            <input type="text" wire:model="name" class="w-full border border-gray-300 rounded px-3 py-2">
            @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>

        <!-- Account type -->
        <div>
            <label class="block text-sm font-medium mb-1">{{ __('Account type') }}</label>
            <div class="grid grid-cols-3 gap-2">
                @foreach($accountTypes as $id => $label)
                    <label class="flex items-center">
                        <input type="checkbox" value="{{ $id }}" wire:model="account_type_id"
                               class="mr-2">
                        {{ $label }}
                    </label>
                @endforeach
            </div>
        </div>

        <!-- Particular -->
        @if(in_array($particularId = $accountTypes->flip()['Particular'] ?? null, $account_type_id))
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-1">{{ __('Profession') }}</label>
                    <input type="text" wire:model="profession" class="w-full border border-gray-300 rounded px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">{{ __('Age range') }}</label>
                    <select wire:model="age_range" class="w-full border border-gray-300 rounded px-3 py-2">
                        <option value="">{{ __('Select...') }}</option>
                        @foreach($ageRanges as $key => $label)
                            <option value="{{ $key }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        @endif

        <!-- Professional -->
        @if(in_array($professionalId = $accountTypes->flip()['Professional'] ?? null, $account_type_id))
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-1">{{ __('Professional category') }}</label>
                    <select wire:model="professional_category_id" class="w-full border border-gray-300 rounded px-3 py-2">
                        <option value="">{{ __('Select...') }}</option>
                        @foreach($professionalCategories as $cat)
                            <option value="{{ $cat->id }}">{{ __(ucwords($cat->name)) }}</option>
                        @endforeach
                    </select>
                </div>
                @if($professional_category_id && optional($professionalCategories->firstWhere('id', $professional_category_id))?->is_other)
                    <div>
                        <label class="block text-sm font-medium mb-1">{{ __('Other') }}</label>
                        <input type="text" wire:model="professional_other_category" class="w-full border border-gray-300 rounded px-3 py-2">
                    </div>
                @endif
            </div>
        @endif

        <!-- Other -->
        @if(in_array($otherId = $accountTypes->flip()['Other'] ?? null, $account_type_id))
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-1">{{ __('Type of establishment') }}</label>
                    <select wire:model="other_category_id" class="w-full border border-gray-300 rounded px-3 py-2">
                        <option value="">{{ __('Select...') }}</option>
                        @foreach($otherCategories as $cat)
                            <option value="{{ $cat->id }}">{{ __(ucwords($cat->name)) }}</option>
                        @endforeach
                    </select>
                </div>
                @if($other_category_id && optional($otherCategories->firstWhere('id', $other_category_id))?->is_other)
                    <div>
                        <label class="block text-sm font-medium mb-1">{{ __('Other') }}</label>
                        <input type="text" wire:model="other_other_category" class="w-full border border-gray-300 rounded px-3 py-2">
                    </div>
                @endif
            </div>
        @endif

        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded">
            {{ __('Save') }}
        </button>

        @if(session()->has('message'))
            <div class="text-green-600 text-sm mt-2">{{ session('message') }}</div>
        @endif
    </form>
</div> --}}