<div class="space-y-8"
    x-data="{
        lastAutotranslatedItem: null,
        selectedItemValueIds: [],
        selectedItemKeyIds: [],
        isProcessing: false,
        selectAllForAutotranslate: false,
        isDeletingKey: false,

        currentInterfaceKeys: [],

        init() {
            window.addEventListener('interface-keys-updated-{{ $this->getId() }}', event => {
                this.currentInterfaceKeys = event.detail.keys || [];
            });
        },

        handleAutotranslationConfirmed(event) {
            const itemData = event.detail.item;
            this.lastAutotranslatedItem = itemData;
            console.log('[Alpine] Single autotranslation confirmed for item:', itemData);
            if (typeof this.$wire !== 'undefined') {
                this.isProcessing = true;
                this.$wire.applyAutoTranslation([itemData.baseValue.id])
                    .then(result => {
                        console.log('[Alpine] Single autotranslate call completed.', result);
                    })
                    .catch(error => {
                        console.error('[Alpine] Error calling single autotranslate:', error);
                    })
                    .finally(() => {
                        this.isProcessing = false;
                        this.selectedItemValueIds = [];
                    });
            } else {
                console.error('[Alpine] $wire is not available for single autotranslate.');
            }
        },

        handleSelectAllChange() {
            const checkboxes = document.querySelectorAll('input[type=checkbox][data-item-id-for-selectall]');
            this.selectedItemKeyIds = [];
            this.selectedItemValueIds = [];

            if (!this.selectAllForAutotranslate) {
                return;
            }

            checkboxes.forEach(cb => {
                const itemId = parseInt(cb.value);
                const itemKeyId = parseInt(cb.getAttribute('keyid'));
                if (itemKeyId) {
                    this.selectedItemKeyIds.push(itemKeyId);
                }
                if (itemId) {
                    this.selectedItemValueIds.push(itemId);
                }
            });

            console.log(this.selectedItemValueIds, this.selectedItemKeyIds);
        },

        toggleAutotranslateSelection(itemId) {
            const index = this.selectedItemValueIds.indexOf(itemId);
            if (index > -1) {
                this.selectedItemValueIds.splice(index, 1);
            } else {
                this.selectedItemValueIds.push(itemId);
            }
        },

        toggleKeySelection(keyId) {
            const index = this.selectedItemKeyIds.indexOf(keyId);
            if (index > -1) {
                this.selectedItemKeyIds.splice(index, 1);
            } else {
                this.selectedItemKeyIds.push(keyId);
            }
        },

        triggerBulkAutotranslate() {
            if (this.selectedItemValueIds.length === 0) {
                return;
            }
            const baseMessage = '{!! t('core.are_you_sure_you_want_to_apply_autotranslation_to_length_selected_item_s') !!}';

            this.$dispatch('open-confirmation-modal', {
                title: '{!! t('core.confirm_bulk_autotranslate') !!}',
                message: '{!! t('core.are_you_sure_you_want_to_apply_autotranslation_to_length_selected_item_s', ['lenght' => '__ITEM_LENGTH_PLACEHOLDER__']) !!}'.replace('__ITEM_LENGTH_PLACEHOLDER__', this.selectedItemValueIds.length),
                confirmText: '{!! t('core.yes_autotranslate_all') !!}',
                cancelText: '{!! t('core.cancel') !!}',
                confirmEventName: 'bulk-autotranslation-confirmed',
                eventData: { itemIds: [...this.selectedItemValueIds] }
            });
        },

        triggerBulkDelete() {
            if (this.selectedItemKeyIds.length === 0) {
                return;
            }
            const baseMessage = '{!! t('core.are_you_sure_you_want_to_apply_autotranslation_to_length_selected_item_s') !!}';

            this.$dispatch('open-confirmation-modal', {
                title: '{!! t('core.confirm_bulk_delete') !!}',
                message: '{!! t('core.are_you_sure_you_want_to_delete_to_length_selected_item_s', ['lenght' => '__ITEM_LENGTH_PLACEHOLDER__']) !!}'.replace('__ITEM_LENGTH_PLACEHOLDER__', this.selectedItemKeyIds.length),
                confirmText: '{!! t('core.yes_delete_all') !!}',
                cancelText: '{!! t('core.cancel') !!}',
                confirmEventName: 'bulk-delete-confirmed',
                eventData: { itemIds: [...this.selectedItemKeyIds] }
            });
        },

        handleBulkAutotranslationConfirmed(event) {
            const itemIdsToProcess = event.detail.itemIds;
            console.log('[Alpine] Bulk autotranslation confirmed for item IDs:', itemIdsToProcess);
            if (itemIdsToProcess && itemIdsToProcess.length > 0 && typeof this.$wire !== 'undefined') {
                this.isProcessing = true;
                this.$wire.applyAutoTranslation(itemIdsToProcess)
                    .then(result => {
                        if (result && result.success) {
                            this.selectedItemValueIds = [];
                            console.log('[Alpine] Bulk autotranslate call successful.');
                        } else {
                            console.error('[Alpine] Bulk autotranslate call failed on server.', result?.message);
                        }
                    })
                    .catch(error => {
                        console.error('[Alpine] Error calling bulk autotranslate Livewire method:', error);
                    })
                    .finally(() => {
                        this.isProcessing = false;

                        const checkAll = document.querySelector('input[type=checkbox][checkbox-all-items]');

                        checkAll.checked = false;
                        checkAll.indeterminate = false;
                    });
            } else if (typeof this.$wire === 'undefined') {
                 console.error('[Alpine] $wire is not available for bulk autotranslate.');
            }
        },

        handleBulkDeleteConfirmed(event) {
            const itemIdsToProcess = event.detail.itemIds;
            console.log('[Alpine] Bulk delete confirmed for item IDs:', itemIdsToProcess);
            if (itemIdsToProcess && itemIdsToProcess.length > 0 && typeof this.$wire !== 'undefined') {
                this.isProcessing = true;
                this.$wire.bulkDelete(itemIdsToProcess)
                    .then(result => {
                        if (result && result.success) {
                            this.selectedItemKeyIds = [];
                            console.log('[Alpine] Bulk delete call successful.');
                        } else {
                            console.error('[Alpine] Bulk delete call failed on server.', result?.message);
                        }
                    })
                    .catch(error => {
                        console.error('[Alpine] Error calling bulk delete Livewire method:', error);
                    })
                    .finally(() => {
                        this.isProcessing = false;

                        const checkAll = document.querySelector('input[type=checkbox][checkbox-all-items]');

                        checkAll.checked = false;
                        checkAll.indeterminate = false;
                    });
            } else if (typeof this.$wire === 'undefined') {
                 console.error('[Alpine] $wire is not available for bulk delete.');
            }
        },

        handleKeyDeletionConfirmed(event) {
            event.stopPropagation();

            if (event.detail.itemId) {
                this.isDeletingKey = true;
                this.$wire.deleteTranslationKey(event.detail.itemId)
                    .then(result => {
                        if (result && result.success) {
                            console.log('[Alpine] delete translation key successful.');
                        } else {
                            console.error('[Alpine] delete translation key failed on server.', result?.message);
                        }
                    })
                    .catch(error => {
                        console.error('[Alpine] Error calling bulk autotranslate Livewire method:', error);
                    })
                    .finally(() => {
                        this.isDeletingKey = false;
                    });
                } else {
                    console.error('[Alpine] $wire is not available for key deletion.');
                }
        },

        copySelectedToClipboard() {
            const allKeys = this.currentInterfaceKeys || [];
            const selected = this.selectedItemKeyIds || [];

            const filtered = allKeys
                .filter(i => selected.includes(i.id))
                .map(i => ({
                    key: i.baseValue?.key || i.key || '',
                    en: i.baseValue?.value || '',
                    fr: i.translatedValue?.value || i.translatedValue?.autotranslatedValue || '',
                }));
                
                const json = JSON.stringify(filtered, null, 4);
                
                navigator.clipboard.writeText(json).then(() => {
                    window.dispatchEvent(new CustomEvent('notify', {
                    detail: {
                        title: 'Copied to clipboard',
                        type: 'success',
                        message: 'Selected translations have been copied as JSON.'
                    }
                }));
            }).catch(err => {
                console.error('Clipboard copy failed', err);
                alert('Failed to copy to clipboard.');
            });

            this.$wire.copyToClipboardNotif();
        }
    }"
    @item-autotranslation-confirmed.window="handleAutotranslationConfirmed($event)"
    @bulk-autotranslation-confirmed.window="handleBulkAutotranslationConfirmed($event)"
    @bulk-delete-confirmed.window="handleBulkDeleteConfirmed($event)"
    @delete-translation-key-confirmed.window="handleKeyDeletionConfirmed($event)"
>
    {{-- Widget Section --}}
    <div class="fi-wi-stats-overview-stats-ctn grid gap-6 md:grid-cols-3">
        <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 text-red-500">
            <div class="grid gap-y-2">
                <div class="flex items-center gap-x-2">
                    <span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ t('core.english_words') }}
                    </span>
                </div>
                <div class="fi-wi-stats-overview-stat-value text-3xl font-semibold tracking-tight text-gray-950 dark:text-white">
                    {{ localizedNumber($languageStats['wordCount'] ?: 0)  }}
                </div>
            </div>
        </div>

        <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 text-red-500">
            <div class="grid gap-y-2">
                <div class="flex items-center gap-x-2">
                    <span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ t('core.keys') }}
                    </span>
                </div>
                <div class="fi-wi-stats-overview-stat-value text-3xl font-semibold tracking-tight text-gray-950 dark:text-white">
                    {{ localizedNumber($languageStats['keyCount'] ?: 0) }}
                </div>
            </div>
        </div>
    </div>

    {{-- Filter Section --}}
    <div class="p-4 bg-white rounded-lg shadow dark:bg-gray-900 dark:ring-white/10">
        <div class="flex justify-between items-center mb-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ t('core.filters') }}</h3>
            <div class="flex gap-4">
                <button @click="$dispatch('open-api-create-modal')" type="button"
                    class="inline-flex items-center px-4 py-2 bg-secondary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-secondary-500 active:bg-secondary-700 focus:outline-none focus:border-secondary-700 focus:ring ring-secondary-300 disabled:opacity-25 transition ease-in-out duration-150 dark:bg-secondary-500 dark:hover:bg-secondary-400">
                    {{ t('core.batch_create') }}
                </button>
                <button @click="$dispatch('open-bulk-create-modal')" type="button"
                    class="inline-flex items-center px-4 py-2 bg-secondary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-secondary-500 active:bg-secondary-700 focus:outline-none focus:border-secondary-700 focus:ring ring-secondary-300 disabled:opacity-25 transition ease-in-out duration-150 dark:bg-secondary-500 dark:hover:bg-secondary-400">
                    {{ t('core.bulk_create') }}
                </button>
                <button @click="$dispatch('open-create-modal')" type="button"
                    class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-500 active:bg-primary-700 focus:outline-none focus:border-primary-700 focus:ring ring-primary-300 disabled:opacity-25 transition ease-in-out duration-150 dark:bg-primary-500 dark:hover:bg-primary-400">
                    {{ t('core.create_new_key') }}
                </button>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4"
             wire:loading.class="opacity-50 cursor-not-allowed"
             wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage">
            <div>
                {{-- Assuming x-forms.language-select passes attributes to its root select/input --}}
                <x-forms.language-select
                    :options="$languageOptions"
                    wire:model.live="selectedLanguage"
                    :label="t('core.language')"
                    :placeholder="t('core.choose_language')"
                    id="primaryLanguageSelector"
                    wire:loading.attr="disabled"
                    wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                    :required="true"
                />
            </div>
            <div>
                <x-forms.language-select
                    :options="$languageOptions"
                    wire:model.live="selectedComparisonLanguage"
                    :label="t('core.comparison_language')"
                    :placeholder="t('core.choose_comparison_language')"
                    id="comparisonLanguageSelector" {{-- Changed ID to be unique --}}
                    wire:loading.attr="disabled"
                    wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                />
            </div>
            <div>
                <label for="translatedFilter"
                    class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.show') !!}</label>
                <select wire:model.live="translatedFilter" id="translatedFilter"
                    wire:loading.attr="disabled"
                    wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="all">{!! t('core.all') !!}</option>
                    <option value="translated">{!! t('core.translated') !!}</option>
                    <option value="untranslated">{!! t('core.untranslated') !!}</option>
                </select>
            </div>
            <div>
                <div class="flex gap-4 items-center">
                    <label for="searchTerm" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.search_by') !!}</label>
                    
                    <div class="flex items-center space-x-4 text-sm">
                        <label class="flex items-center">
                            <input type="radio" wire:model.live="searchBy" value="key"
                                wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                                class="text-primary-600 focus:ring-primary-500">
                            <span class="ml-1">{{ t('core.key') }}</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" wire:model.live="searchBy" value="value"
                                wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                                class="text-primary-600 focus:ring-primary-500">
                            <span class="ml-1">{{ t('core.value') }}</span>
                        </label>
                    </div>
                </div>

                <input wire:model.live.debounce.500ms="searchTerm" type="text" id="searchTerm" placeholder="{!! t('core.search_keys_or_values') !!}"
                       wire:loading.attr="disabled"
                       wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            </div>
            <div>
                <label for="selectedApp" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.app') !!}</label>
                <select wire:model.live="selectedApp" id="selectedApp"
                    wire:loading.attr="disabled"
                    wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="">{!! t('core.all_apps_or_select_an_app') !!}</option>
                    <option value="0">{!! t('core.commons') !!}</option>
                    @foreach ($apps as $app)
                        <option value="{{ $app['id'] }}">{{ $app['name'] }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex items-end justify-end" x-show="selectedItemValueIds.length > 0" x-transition>
                <x-filament::dropdown placement="bottom-end">
                    <x-slot name="trigger">
                        <x-filament::button icon="heroicon-o-chevron-down" iconPosition="after">
                            {!! t('core.selected_value') !!} (<span x-text="selectedItemValueIds.length"></span>)
                        </x-filament::button>
                    </x-slot>

                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item
                            tag="button"
                            icon="heroicon-o-language"
                            @click="triggerBulkAutotranslate()"
                        >
                            {!! t('core.autotranslate_selected') !!}
                        </x-filament::dropdown.list.item>

                        <x-filament::dropdown.list.item
                            tag="button"
                            icon="heroicon-o-arrow-path-rounded-square"
                            @click="copySelectedToClipboard"
                        >
                            {!! t('core.generate_json') !!}
                        </x-filament::dropdown.list.item>

                        <x-filament::dropdown.list.item
                            @click="triggerBulkDelete()"
                            tag="button"
                            icon="heroicon-o-trash"
                            color="danger"
                        >
                            {!! t('core.delete_selected') !!}
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>
        </div>
    </div>

    {{-- Table Section --}}
    <div class="relative bg-white rounded-lg shadow overflow-x-auto dark:bg-gray-900 dark:ring-white/10">
        {{-- Loading Overlay for Table --}}
        <div wire:loading.flex wire:target="selectedLanguage, selectedComparisonLanguage, translatedFilter, searchTerm, selectedApp, nextPage, previousPage"
             class="absolute inset-0 z-30 items-center justify-center bg-white bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75">
            <svg class="animate-spin h-8 w-8 text-primary-600 dark:text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>

        {{-- Loading Overlay for Autotranslation Actions --}}
        <div x-show="isProcessing || isDeletingKey"
            x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
            x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
            class="absolute inset-0 z-30 flex items-center justify-center bg-gray-300 bg-opacity-50 dark:bg-gray-700 dark:bg-opacity-50">
            <svg class="animate-spin h-10 w-10 text-primary-600 dark:text-primary-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>

        @if ($selectedLanguage && isset($selectedLanguage['id']) && $selectedComparisonLanguage && isset($selectedComparisonLanguage['id']))
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                <thead class="bg-gray-50 dark:bg-gray-900 dark:ring-white/10">
                    <tr>
                        <th scope="col" class="p-4">
                            @if (count($interfaceKeys) > 0)
                            <div class="flex items-center">
                                <input id="checkbox-all-items"
                                       type="checkbox"
                                       x-model="selectAllForAutotranslate"
                                       @change="handleSelectAllChange()"
                                       checkbox-all-items
                                       class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer">
                                <label for="checkbox-all-items" class="sr-only">{!! t('core.select_all_items_for_autotranslate') !!}</label>
                            </div>
                            @endif
                        </th>
                        <th
                            scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300"
                            style="width: 250px; max-width: 250px; white-space: nowrap;"
                        >{!! t('core.key') !!}</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">{!! t('core.app') !!}</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium whitespace-nowrap text-gray-500 uppercase tracking-wider dark:text-gray-300">
                           {!! t('core.base_value') !!} @if (isset($selectedLanguage['flagCode']))<span class="flag-icon flag-icon-{{ $selectedLanguage['flagCode'] }}"></span>@endif
                        </th>
                        @if ($selectedLanguage && $selectedComparisonLanguage && $selectedLanguage['id'] != $selectedComparisonLanguage['id'])
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                {!! t('core.comparison_value') !!} @if (isset($selectedComparisonLanguage['flagCode']))<span class="flag-icon flag-icon-{{ $selectedComparisonLanguage['flagCode'] }}"></span>@endif
                            </th>
                        @endif
                        @if ($selectedLanguage && $selectedLanguage['id'] != 1)
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                {!! t('core.translated_value') !!} @if (isset($selectedLanguage['flagCode']))<span class="flag-icon flag-icon-{{ $selectedLanguage['flagCode'] }}"></span>@endif
                            </th>
                        @endif
                        <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                    </tr>
                </thead>
                {{-- <pre>{{ json_encode($interfaceKeys[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                <pre>{{ json_encode($interfaceKeys[1], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre> --}}
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                    @php
                        $title = t('core.use_autotranslate');
                    @endphp
                    @forelse($interfaceKeys as $item)
                        <tr wire:key="item-row-{{ $item['baseValue']['id'] }}">
                            <td class="w-4 p-4">
                                @if(isset($item['baseValue']['id'])) {{-- Only show checkbox if autotranslate is possible --}}
                                <div class="flex items-center">
                                    <input :id="'checkbox-item-' + {{ $item['baseValue']['id'] }}"
                                           type="checkbox"
                                           :value="{{ $item['baseValue']['id'] }}"
                                           :keyid="{{ $item['id'] }}"
                                           @change="toggleAutotranslateSelection({{ $item['baseValue']['id'] }}); toggleKeySelection({{ $item['id'] }})"
                                           :checked="selectedItemValueIds.includes({{ $item['baseValue']['id'] }})"
                                           data-item-id-for-selectall
                                           class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer">
                                    <label :for="'checkbox-item-' + {{ $item['baseValue']['id'] }}" class="sr-only">{!! t('core.select_item_for_autotranslate') !!}</label>
                                </div>
                                @endif
                            </td>
                            <td
                                class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white line-clamp-2 flex items-center"
                                style="width: 250px; max-width: 250px; word-break: break-word; "
                            >
                                <span class="flex gap-1 items-center text-wrap" title="{!! $item['key'] !!}">
                                    <span class="line-clamp-2">
                                        {!! $item['key'] ?? '<span class="text-gray-400 italic">N/A</span>' !!}
                                    </span>
                                    <button type="button"
                                            @click="$dispatch('open-edit-key-app-modal', { item: {{ Illuminate\Support\Js::from($item) }} })"
                                            title="Edit Key/App"
                                            class="text-secondary-600 hover:text-secondary-900 dark:text-secondary-500 dark:hover:text-secondary-400 mr-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                                            <path d="M10.032 2.2a1.25 1.25 0 0 1 1.768 0l1.96 1.96a1.25 1.25 0 0 1 0 1.768L6.031 13.657a1.249 1.249 0 0 1-.648.398l-2.56.64a.75.75 0 0 1-.94-.94l.64-2.56a1.25 1.25 0 0 1 .397-.648L10.032 2.2ZM5 12.5a.5.5 0 1 0-1 0 .5.5 0 0 0 1 0Z" /> <path d="M11.5 4a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Z" />{{-- Icon for key/settings --}}
                                        </svg>
                                    </button>
                                </span>
                            </td>
                            {{-- ... (other tds remain the same) ... --}}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300" >
                                {!! $item['app']['name'] ?? "<span class='text-gray-300 italic'>" . t('core.commons') . "</span>" !!}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-300" >
                                {!! $item['baseValue']['value'] ?? '<span class="text-gray-300 italic">N/A</span>'  !!}
                            </td>
                            @if ($selectedLanguage && $selectedComparisonLanguage && $selectedLanguage['id'] != $selectedComparisonLanguage['id'])
                                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-300" >
                                    {!! $item['translatedValue']['value'] ?? '<span class="text-gray-300 italic">Not translated yet</span>' !!}
                                </td>
                            @endif
                            @if ($selectedLanguage && $selectedLanguage['id'] != 1)
                                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                                    {{ $item['baseValue']['autotranslatedValue'] ?? '' }}
                                </td>
                            @endif
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                @if($selectedLanguage['id'] != 1 && isset($item['id']))
                                    @php
                                        $itemKeyForJs = Illuminate\Support\Js::from($item['key']);
                                        $itemDataForJs = Illuminate\Support\Js::from($item);
                                    @endphp
                                    <button
                                        type="button"
                                        @click="
                                            const itemKey = {{ $itemKeyForJs }};
                                            const itemPayload = {{ $itemDataForJs }};
                                            $dispatch('open-confirmation-modal', {
                                                title: '{{ $title }}',
                                                message: '{!! t('core.apply_autotranslated_value_for_this_key', ['itemKey' => $item['key']]) !!}',
                                                confirmText: '{!! t('core.yes_apply', ) !!}',
                                                cancelText: '{!! t('core.no', ) !!}',
                                                confirmEventName: 'item-autotranslation-confirmed',
                                                eventData: { item: itemPayload }
                                            })"
                                        title="Auto-translate this item"
                                        class="text-secondary-600 hover:text-secondary-900 dark:text-secondary-500 dark:hover:text-secondary-400 mr-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                        :disabled="isProcessing"
                                    >
                                        <svg x-show="isProcessing && lastAutotranslatedItem && lastAutotranslatedItem.id === {{ $item['id'] }}" class="animate-spin h-4 w-4 text-secondary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <svg x-show="!(isProcessing && lastAutotranslatedItem && lastAutotranslatedItem.id === {{ $item['id'] }})" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                                            <path fill-rule="evenodd" d="M11 5a.75.75 0 0 1 .688.452l3.25 7.5a.75.75 0 1 1-1.376.596L12.89 12H9.109l-.67 1.548a.75.75 0 1 1-1.377-.596l3.25-7.5A.75.75 0 0 1 11 5Zm-1.24 5.5h2.48L11 7.636 9.76 10.5ZM5 1a.75.75 0 0 1 .75.75v1.261a25.27 25.27 0 0 1 2.598.211.75.75 0 1 1-.2 1.487c-.22-.03-.44-.056-.662-.08A12.939 12.939 0 0 1 5.92 8.058c.237.304.488.595.752.873a.75.75 0 0 1-1.086 1.035A13.075 13.075 0 0 1 5 9.307a13.068 13.068 0 0 1-2.841 2.546.75.75 0 0 1-.827-1.252A11.566 11.566 0 0 0 4.08 8.057a12.991 12.991 0 0 1-.554-.938.75.75 0 1 1 1.323-.707c.**************.15.271.388-.68.708-1.405.952-2.164a23.941 23.941 0 0 0-*********.75 0 0 1-.2-1.487c.853-.114 1.72-.185 2.598-.211V1.75A.75.75 0 0 1 5 1Z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                @endif
                                @if($selectedLanguage && $selectedComparisonLanguage && isset($item['id']))
                                    <button
                                        type="button"
                                        @click="$dispatch('open-edit-modal', { itemId: {{ $item['id'] }} })"
                                        title="Edit"
                                        class="text-primary-600 hover:text-primary-900 dark:text-primary-500 dark:hover:text-primary-400">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                                            <path d="M13.488 2.513a1.75 1.75 0 0 0-2.475 0L6.75 6.774a2.75 2.75 0 0 0-.596.892l-.848 2.047a.75.75 0 0 0 .98.98l2.047-.848a2.75 2.75 0 0 0 .892-.596l4.261-4.262a1.75 1.75 0 0 0 0-2.474Z" />
                                            <path d="M4.75 3.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V9A.75.75 0 0 1 14 9v2.25A2.75 2.75 0 0 1 11.25 14h-6.5A2.75 2.75 0 0 1 2 11.25v-6.5A2.75 2.75 0 0 1 4.75 2H7a.75.75 0 0 1 0 1.5H4.75Z" />
                                        </svg>
                                    </button>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" {{-- Adjusted colspan --}}
                                class="px-6 py-10 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">
                                @if (!$selectedLanguage || !$selectedComparisonLanguage)
                                    {!! t('core.please_select_both_a_language_and_a_comparison_language') !!}
                                @else
                                    {!! t('core.no_translation_keys_found_for_the_selected_criteria') !!}
                                @endif
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        @endif
    </div>

    {{-- Pagination Section --}}
    @if ($totalInterfaceKeys > 0 && $interfaceKeys && count($interfaceKeys) > 0)
        <div wire:loading.class="opacity-50 cursor-not-allowed"
             wire:target="nextPage, previousPage">
            <div class="flex justify-between items-center px-4 py-3 sm:px-6">
                <div class="text-sm text-gray-700 dark:text-gray-200">
                    {!! t('core.showing_to_of', [
                        'page' => (($this->paginators['page'] ?? 1) - 1) * $perPage + 1,
                        'limit' => min(($this->paginators['page'] ?? 1) * $perPage, $totalInterfaceKeys),
                        'all' => $totalInterfaceKeys,
                    ]) !!}

                </div>
                <div>
                    @if (($this->paginators['page'] ?? 1) > 1)
                        <button wire:click="previousPage('page')"
                                wire:loading.attr="disabled" wire:target="previousPage"
                                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600">
                            {!! t('core.previous') !!}
                        </button>
                        @endif
                        @if (($this->paginators['page'] ?? 1) * $perPage < $totalInterfaceKeys)
                        <button wire:click="nextPage('page')"
                        wire:loading.attr="disabled" wire:target="nextPage"
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600">
                            {!! t('core.next') !!}
                        </button>
                    @endif
                </div>
            </div>
        </div>
    @endif

    {{-- Edit Modal --}}
    <div
        x-data="{
            showModal: false,
            editingItem: null,
            appId: null,
            formKey: '',
            formBaseValue: '',
            formComparisonValue: '',
            allItemsForModal: [],
            isLoading: false,

            init() {
                this.$watch('showModal', (value) => {
                    if (!value) {
                        this.resetForm();
                        this.isLoading = false;
                    }
                });

                window.addEventListener('interface-keys-updated-{{ $this->getId() }}', event => {
                    this.allItemsForModal = event.detail.keys || (event.detail[0]?.keys || []);
                });
            },

            findItemById(id) {
                if (!Array.isArray(this.allItemsForModal)) {
                    console.error('[ALPINE FIND] allItemsForModal is not an array!', this.allItemsForModal);
                    return null;
                }
                return this.allItemsForModal.find(i => i && i.id == id);
            },

            openModalWithId(itemId) {
                if (this.isLoading) return;
                const itemToEdit = this.findItemById(itemId);

                if (itemToEdit) {
                    this.editingItem = itemToEdit;
                    this.formKey = itemToEdit.key || '';
                    this.formBaseValue = itemToEdit.baseValue?.value || '';
                    this.formComparisonValue = itemToEdit.translatedValue?.value || '';
                    this.appId = itemToEdit.appId || null;
                    this.isLoading = false;
                    this.showModal = true;
                    this.$nextTick(() => { this.$refs.editBaseValueInput?.focus(); });
                } else {
                    console.error('Could not find item with ID:', itemId, 'in Alpine allItemsForModal.');
                }
            },

            resetForm() {
                this.editingItem = null;
                this.formKey = '';
                this.formBaseValue = '';
                this.formComparisonValue = '';
                this.appId = null;
            },

            saveChanges() {
                if (!this.editingItem || this.isLoading) return;

                this.isLoading = true;

                this.$wire.call('saveItemFromModal',
                    this.editingItem.id,
                    this.formKey,
                    this.formBaseValue,
                    this.formComparisonValue,
                    this.appId
                ).then(success => {
                    if (success) {
                        this.showModal = false;
                    }
                }).catch(error => {
                    console.error('Error saving item:', error);
                }).finally(() => {
                    this.isLoading = false;
                });
            }
        }"
        @open-edit-modal.window="openModalWithId($event.detail.itemId)"
        x-show="showModal"
        x-on:keydown.escape.window="if (!isLoading) showModal = false"
        class="fixed inset-0 z-50 overflow-y-auto"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
        style="display: none;"
    >
        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showModal" x-transition.opacity class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div x-show="showModal"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                @click.away="if (!isLoading) showModal = false"
                class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg dark:bg-gray-800"
            >
                <div class="flex items-center justify-between pb-3 border-b dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white" id="modal-title">
                        {!! t('core.edit_translation_key') !!}
                        <span x-text="editingItem ? '(' + editingItem.key + ')' : ''" class="text-sm text-gray-500"></span>
                    </h3>
                    <button @click="if (!isLoading) showModal = false"
                            type="button"
                            :disabled="isLoading"
                            class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                </div>

                <div class="mt-4 space-y-4">
                    <div>
                        <label for="alpineEditFormKey" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.key_string') !!}</label>
                        <input x-model="formKey" type="text" id="alpineEditFormKey" readonly
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    </div>

                    <div>
                        <label for="alpineEditFormBaseValue" class="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            @if ($selectedLanguage && $selectedLanguage['flagCode'])
                                <span class="flag-icon flag-icon-{{ $selectedLanguage['flagCode'] }}"></span>
                            @endif
                            {!! t('core.base_value') !!}
                        </label>
                        <textarea x-model="formBaseValue" id="alpineEditFormBaseValue" x-ref="editBaseValueInput" rows="3"
                        :disabled="isLoading"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-75"></textarea>
                    </div>

                    @if ($selectedComparisonLanguage && $selectedLanguage && $selectedLanguage['id'] != 1 && $selectedComparisonLanguage['id'] != $selectedLanguage['id'])
                    <div>
                        <label for="alpineEditFormComparisonValue" class="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            @if ($selectedComparisonLanguage && $selectedComparisonLanguage['flagCode'])
                            <span class="flag-icon flag-icon-{{ $selectedComparisonLanguage['flagCode'] }}"></span>
                            @endif
                                {!! t('core.comparison_value') !!}
                            </label>
                            <textarea x-model="formComparisonValue" id="alpineEditFormComparisonValue" rows="3" readonly
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed" disabled></textarea>
                        </div>
                    @endif
                </div>

                <div class="mt-6 sm:flex sm:flex-row-reverse">
                    <button @click="saveChanges()" type="button"
                            :disabled="isLoading"
                            class="inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto dark:bg-primary-500 dark:hover:bg-primary-600 disabled:opacity-75 disabled:cursor-not-allowed">
                        <svg x-show="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-show="isLoading">{!! t('core.saving') !!}</span>
                        <span x-show="!isLoading">{!! t('core.save_changes') !!}</span>
                    </button>
                    <button @click="if (!isLoading) showModal = false"
                            type="button"
                            :disabled="isLoading"
                            class="inline-flex justify-center w-full px-4 py-2 mt-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                        {!! t('core.cancel') !!}
                    </button>
                </div>
            </div>
        </div>
    </div>
    {{-- End Edit Modal --}}

    {{-- Create Modal --}}
    <div
        x-data="{
            showCreate: false,
            formKey: '',
            formAppId: 0,
            formBaseValue: '',
            formComparisonValue: '',
            isLoading: false,

            init() {
                this.$watch('showCreate', open => {
                    if (open) {
                        this.resetAlpineCreateForm();
                        this.$nextTick(() => { this.$refs.createFormKeyInput?.focus(); });
                    } else {
                        this.isLoading = false;
                    }
                });
            },

            resetAlpineCreateForm() {
                this.formKey = '';
                this.formAppId = 0;
                this.formBaseValue = '';
                this.formComparisonValue = '';
            },

            openModalHandler() {
                if (this.isLoading) return;
                this.isLoading = false;
                this.showCreate = true;
                this.$wire.openCreateModal();
            },

            submitCreateForm() {
                if (this.isLoading) return;
                this.isLoading = true;

                this.$wire.call('createNewItem',
                    this.formKey,
                    this.formAppId,
                    this.formBaseValue,
                    this.formComparisonValue,
                ).then(result => {
                    if (result && result.success) {
                        this.showCreate = false;
                    } else {
                        console.error('Create item failed:', result?.message);
                    }
                }).catch(error => {
                    console.error('Error creating item:', error);
                }).finally(() => {
                    this.isLoading = false;
                });
            }
        }"
        @open-create-modal.window="openModalHandler()"
        x-show="showCreate"
        x-on:keydown.escape.window="if (!isLoading) showCreate = false"
        class="fixed inset-0 z-50 overflow-y-auto"
        aria-labelledby="create-modal-title"
        role="dialog"
        aria-modal="true"
        style="display: none;"
    >
        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showCreate" x-transition.opacity class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="showCreate"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                @click.away="if (!isLoading) showCreate = false"
                class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg dark:bg-gray-800"
            >
                <div class="flex items-center justify-between pb-3 border-b dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white" id="create-modal-title">
                        {!! t('core.create_new_translation_key') !!}
                    </h3>
                    <button @click="if (!isLoading) showCreate = false"
                            type="button"
                            :disabled="isLoading"
                            class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                </div>

                <form @submit.prevent="submitCreateForm">
                    <div class="mt-4 space-y-4">
                        <div>
                            <label for="alpineCreateFormKey" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.key_string') !!} <span class="text-red-500">*</span></label>
                            <input x-model="formKey" type="text" id="alpineCreateFormKey" x-ref="createFormKeyInput" required
                                :disabled="isLoading"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white @error('createFormKey') border-red-500 @enderror disabled:opacity-75">
                            @error('createFormKey') <span class="text-xs text-red-500">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="alpineCreateFormAppId" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.application') !!} <span class="text-red-500">*</span></label>
                            <select x-model="formAppId" id="alpineCreateFormAppId" required
                                    :disabled="isLoading" {{-- <--- Disable select --}}
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white @error('createFormAppId') border-red-500 @enderror disabled:opacity-75">
                                <option value="0">{!! t('core.commons_no_specific_app') !!}</option>
                                @foreach ($apps as $app)
                                    <option value="{{ $app['id'] }}">{{ $app['name'] }}</option>
                                @endforeach
                            </select>
                            @error('createFormAppId') <span class="text-xs text-red-500">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="alpineCreateFormBaseValue" class="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                <span class="flag-icon flag-icon-us"></span> {!! t('core.english_value') !!} <span class="text-red-500">*</span>
                            </label>
                            <textarea x-model="formBaseValue" id="alpineCreateFormBaseValue" rows="3" required
                                    :disabled="isLoading" {{-- <--- Disable textarea --}}
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white @error('createFormBaseValue') border-red-500 @enderror disabled:opacity-75"></textarea>
                            @error('createFormBaseValue') <span class="text-xs text-red-500">{{ $message }}</span> @enderror
                        </div>

                        @if ($selectedLanguage && isset($selectedLanguage['id']) && (isset($selectedLanguage['languageISO2']) ? strtolower($selectedLanguage['languageISO2']) !== 'en' : strtolower($selectedLanguage['languageFull'] ?? '') !== 'english') )
                            <div>
                                <label for="alpineCreateFormComparisonValue" class="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    @if (isset($selectedLanguage['flagCode']))
                                        <span class="flag-icon flag-icon-{{ $selectedLanguage['flagCode'] }}"></span>
                                    @endif
                                    {!! t('core.value_for') !!} {{ $selectedLanguage['languageFull'] ?? 'Selected Language' }}
                                </label>
                                <textarea x-model="formComparisonValue" id="alpineCreateFormComparisonValue" rows="3"
                                        :disabled="isLoading" {{-- <--- Disable textarea --}}
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white @error('createFormComparisonValue') border-red-500 @enderror disabled:opacity-75"></textarea>
                                @error('createFormComparisonValue') <span class="text-xs text-red-500">{{ $message }}</span> @enderror
                            </div>
                        @endif
                    </div>

                    <div class="mt-6 sm:flex sm:flex-row-reverse">
                        <button type="submit"
                                :disabled="isLoading"
                                class="inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto dark:bg-primary-500 dark:hover:bg-primary-600 disabled:opacity-75 disabled:cursor-not-allowed">
                            <svg x-show="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span x-show="isLoading">{!! t('core.creating') !!}</span>
                            <span x-show="!isLoading">{!! t('core.create_key') !!}</span>
                        </button>
                        <button @click="if (!isLoading) showCreate = false"
                                type="button"
                                :disabled="isLoading"
                                class="inline-flex justify-center w-full px-4 py-2 mt-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                            {!! t('core.cancel') !!}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{-- End Create Modal --}}

    {{-- New "Edit Key/App Modal" --}}
    <div x-data="{
            showEditKeyAppModal: false,
            editingKeyAppItem: null,
            editKeyAppForm_key: '',
            editKeyAppForm_appId: null,
            isSavingKeyApp: false,
            editKeyApp_errorMessage: '',
            isDeletingKeyApp: false,

            openEditKeyAppModal(item) {
                if (this.isSavingKeyApp || this.isDeletingKeyApp) return;
                this.editingKeyAppItem = item;
                this.editKeyAppForm_key = item.key || '';
                this.editKeyAppForm_appId = item.appId !== null ? item.appId : (item.app ? item.app.id : 0);
                this.isSavingKeyApp = false;
                this.editKeyApp_errorMessage = '';
                this.isDeletingKeyApp = false;
                this.showEditKeyAppModal = true;
                this.$nextTick(() => this.$refs.editKeyAppKeyInput?.focus());
            },

            closeEditKeyAppModal() {
                this.showEditKeyAppModal = false;
                this.editingKeyAppItem = null;
                this.editKeyApp_errorMessage = '';
                this.isDeletingKeyApp = false;
            },

            saveKeyAppChanges() {
                if (!this.editingKeyAppItem || this.isSavingKeyApp || this.isDeletingKeyApp) return;
                this.editKeyApp_errorMessage = '';

                if (!this.editKeyAppForm_key.trim()) {
                    this.editKeyApp_errorMessage = 'Key cannot be empty.';
                    return;
                }

                this.isSavingKeyApp = true;
                this.$wire.call('updateTranslationKeyAndApp',
                    this.editingKeyAppItem.id,
                    this.editKeyAppForm_key,
                    parseInt(this.editKeyAppForm_appId)
                ).then(result => {
                    if (result && result.success) {
                        this.closeEditKeyAppModal();
                    } else {
                        this.editKeyApp_errorMessage = result?.message || 'An error occurred while saving.';
                    }
                }).catch(error => {
                    console.error('Error saving key/app:', error);
                    this.editKeyApp_errorMessage = 'An unexpected network error occurred.';
                }).finally(() => {
                    this.isSavingKeyApp = false;
                });
            },

            triggerDeleteKeyConfirmation() {
                if (!this.editingKeyAppItem || this.isSavingKeyApp || this.isDeletingKeyApp) return;

                this.$dispatch('open-confirmation-modal', {
                    title: 'Confirm Delete Key',
                    message: `Are you sure you want to delete the key: '${this.editingKeyAppItem.key}'? This will delete the key and all its translations. This action cannot be undone.`,
                    confirmText: 'Yes, Delete Key',
                    cancelText: 'Cancel',
                    confirmEventName: 'delete-translation-key-confirmed',
                    eventData: {
                        itemId: this.editingKeyAppItem.id,
                        itemKey: this.editingKeyAppItem.key
                    }
                });
            }
        }"

         @open-edit-key-app-modal.window="openEditKeyAppModal($event.detail.item)"
         x-show="showEditKeyAppModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-[60] overflow-y-auto"
         aria-labelledby="edit-key-app-modal-title" role="dialog" aria-modal="true" style="display: none;">
        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showEditKeyAppModal" x-transition.opacity
                 class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div @click.away="if (!isSavingKeyApp && !isDeletingKeyApp) closeEditKeyAppModal()"
                 x-show="showEditKeyAppModal"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg dark:bg-gray-800">
                <div class="flex items-center justify-between pb-3 border-b dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white" id="edit-key-app-modal-title">
                        {!! t('core.edit_key_application') !!}
                        <span x-text="editingKeyAppItem ? '{!! t('core.for') !!} (' + editingKeyAppItem.key + ')' : ''" class="text-sm text-gray-500"></span>
                    </h3>
                    <button @click="if (!isSavingKeyApp) closeEditKeyAppModal()" type="button" :disabled="isSavingKeyApp"
                            class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 disabled:opacity-50">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                </div>

                <form @submit.prevent="saveKeyAppChanges()">
                    {{-- Display Error Message --}}
                    <div class="mt-4 space-y-4">
                        <div x-show="editKeyApp_errorMessage"
                            x-transition
                            class="p-3 mb-3 text-sm text-red-700 bg-red-100 rounded-md dark:bg-red-200 dark:text-red-800"
                            role="alert">
                            <span class="font-medium">Error:</span> <span x-text="editKeyApp_errorMessage"></span>
                        </div>

                        <div>
                            <label for="editKeyAppKeyInput" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.key_string') !!} <span class="text-red-500">*</span></label>
                            <input type="text" x-model="editKeyAppForm_key" id="editKeyAppKeyInput" x-ref="editKeyAppKeyInput" required :disabled="isSavingKeyApp"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-75"
                                   :class="editKeyApp_errorMessage ? ('border-red-500 dark:border-red-400') : ''">
                            {{-- @error or client-side validation message can go here --}}
                        </div>

                        <div>
                            <label for="editKeyAppAppId" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.application') !!} <span class="text-red-500">*</span></label>
                            <select x-model="editKeyAppForm_appId" id="editKeyAppAppId" required :disabled="isSavingKeyApp"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-75">
                                <option value="0">{!! t('core.commons_no_specific_app') !!}</option>
                                @foreach ($apps as $app) {{-- Assuming $apps is available from Livewire --}}
                                    <option value="{{ $app['id'] }}">{{ $app['name'] }}</option>
                                @endforeach
                            </select>
                            {{-- @error or client-side validation message can go here --}}
                        </div>
                    </div>

                    <div class="mt-6 pt-4 border-t dark:border-gray-700 sm:flex sm:justify-between">
                        {{-- Delete Button (aligned left) --}}
                        <div>
                            <button type="button"
                                    @click.stop="triggerDeleteKeyConfirmation()"
                                    :disabled="isSavingKeyApp || isDeletingKeyApp"
                                    class="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-500 dark:hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed">
                                <svg x-show="isDeletingKeyApp" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                                    <path fill-rule="evenodd" d="M5 3.25V4H2.75a.75.75 0 0 0 0 1.5h.3l.815 8.15A1.5 1.5 0 0 0 5.357 15h5.285a1.5 1.5 0 0 0 1.493-1.35l.815-8.15h.3a.75.75 0 0 0 0-1.5H11v-.75A2.25 2.25 0 0 0 8.75 1h-1.5A2.25 2.25 0 0 0 5 3.25Zm2.25-.75a.75.75 0 0 0-.75.75V4h3v-.75a.75.75 0 0 0-.75-.75h-1.5ZM6.05 6a.75.75 0 0 1 .787.713l.275 5.5a.75.75 0 0 1-1.498.075l-.275-5.5A.75.75 0 0 1 6.05 6Zm3.9 0a.75.75 0 0 1 .712.787l-.275 5.5a.75.75 0 0 1-1.498-.075l.275-5.5a.75.75 0 0 1 .786-.711Z" clip-rule="evenodd" />
                                </svg>

                                <svg x-show="!isDeletingKeyApp" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4 mr-1">
                                    <path fill-rule="evenodd" d="M5 3.25V4H2.75a.75.75 0 0 0 0 1.5h.3l.815 8.15A1.5 1.5 0 0 0 5.361 15h5.278a1.5 1.5 0 0 0 1.496-1.35l.815-8.15h.3a.75.75 0 0 0 0-1.5H11v-.75A2.25 2.25 0 0 0 8.75 1h-1.5A2.25 2.25 0 0 0 5 3.25Zm2.25-.75a.75.75 0 0 0-.75.75V4h3v-.75a.75.75 0 0 0-.75-.75h-1.5ZM4.5 6.5a.75.75 0 0 0-.75.75v5.5a.75.75 0 0 0 1.5 0v-5.5a.75.75 0 0 0-.75-.75Zm3.75.75a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-1.5 0v-5.5a.75.75 0 0 1 .75-.75Z" clip-rule="evenodd" />
                                </svg>
                                <span x-show="isDeletingKeyApp">{!! t('core.deleting') !!}</span>
                                <span x-show="!isDeletingKeyApp">{!! t('core.delete_key') !!}</span>
                            </button>
                        </div>

                        {{-- Save and Cancel Buttons (aligned right) --}}
                        <div class="mt-3 sm:mt-0 sm:ml-3 sm:flex sm:flex-row-reverse">
                            <button type="submit" :disabled="isSavingKeyApp || isDeletingKeyApp"
                                    class="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 dark:bg-primary-500 dark:hover:bg-primary-600 disabled:opacity-75 disabled:cursor-not-allowed">
                                <svg x-show="isSavingKeyApp" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span x-show="isSavingKeyApp">{!! t('core.saving') !!}</span>
                                <span x-show="!isSavingKeyApp">{!! t('core.save') !!}</span>
                            </button>
                            <button @click="if (!isSavingKeyApp && !isDeletingKeyApp) closeEditKeyAppModal()" type="button" :disabled="isSavingKeyApp || isDeletingKeyApp"
                                    class="inline-flex justify-center w-full sm:w-auto px-4 py-2 mt-3 sm:mt-0 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50">
                                {!! t('core.cancel') !!}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{-- End Edit Key/App Modal --}}


    {{-- Bulk Create --}}
    <div
        x-data="{
            show: false,
            isLoading: false,
            errorMessage: '',
            jsonInput: '',
            parsedItems: [],
            parseError: '',

            open() {
                this.show = true;
                this.jsonInput = '';
                this.parsedItems = [];
                this.parseError = '';
                this.errorMessage = '';
                this.$nextTick(() => this.$refs.jsonInput?.focus());
            },

            close() {
                if (!this.isLoading) this.show = false;
            },

            parseJson() {
                this.parseError = '';
                try {
                    let arr = JSON.parse(this.jsonInput);
                    if (!Array.isArray(arr)) throw new Error('JSON must be an array');
                    this.parsedItems = arr;
                } catch (e) {
                    this.parseError = e.message;
                    this.parsedItems = [];
                }
            },

            submit() {
                if (this.isLoading || !this.parsedItems.length) return;
                this.isLoading = true;
                this.errorMessage = '';

                this.$wire.call('bulkCreateTranslations', this.parsedItems)
                    .then(result => {
                        this.show = false;
                    })
                    .catch(error => {
                        this.errorMessage = 'Bulk create failed. Please try again.';
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
        }"
        x-show="show"
        @open-bulk-create-modal.window="open()"
        x-on:keydown.escape.window="close()"
        class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none;"
    >
        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="show" x-transition.opacity class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="show"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                @click.away="close()"
                class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg dark:bg-gray-800"
            >
                <div class="flex items-center justify-between pb-3 border-b dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                        Bulk Create Translations
                    </h3>
                    <button @click="close()" type="button" :disabled="isLoading"
                        class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 disabled:opacity-50">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mt-4 space-y-4">
                    <template x-if="errorMessage">
                        <div class="p-3 text-sm text-red-700 bg-red-100 rounded dark:bg-red-200 dark:text-red-800" x-text="errorMessage"></div>
                    </template>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                        Paste JSON array of translations:
                    </label>
                    <textarea
                        x-model="jsonInput"
                        x-ref="jsonInput"
                        rows="8"
                        class="block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder='[{"key":"core.hello","en":"hello","fr":"bonjour"}]'
                        @input="parseJson()"
                        :disabled="isLoading"
                    ></textarea>
                    <template x-if="parseError">
                        <div class="text-xs text-red-500" x-text="parseError"></div>
                    </template>
                    <template x-if="parsedItems.length">
                        <div class="mt-4">
                            <div class="font-semibold mb-2">Preview:</div>
                            <div class="overflow-x-auto max-h-48 border rounded bg-gray-50 dark:bg-gray-900 p-2 text-xs">
                                <table class="min-w-full">
                                    <thead>
                                        <tr>
                                            <template x-for="(v, k) in parsedItems[0]" :key="k">
                                                <th class="px-2 py-1 border-b" x-text="k"></th>
                                            </template>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <template x-for="row in parsedItems" :key="row.key">
                                            <tr>
                                                <template x-for="(v, k) in row" :key="k">
                                                    <td class="px-2 py-1 border-b" x-text="v"></td>
                                                </template>
                                            </tr>
                                        </template>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="mt-6 sm:flex sm:flex-row-reverse">
                    <button type="button" @click="submit()" :disabled="isLoading || !parsedItems.length"
                        class="inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto dark:bg-primary-500 dark:hover:bg-primary-600 disabled:opacity-75 disabled:cursor-not-allowed">
                        <svg x-show="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Bulk Create
                    </button>
                    <button @click="close()" type="button" :disabled="isLoading"
                        class="inline-flex justify-center w-full px-4 py-2 mt-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
    {{-- Bulk Create End --}}
    
    {{-- Batch Create start --}}
    <div
        x-data="{
            show: false,
            isLoading: false,
            errorMessage: '',
            monacoContent: '',   // ← renamed from jsonInput
            appId: 0,
            languageId: 1,
            namespace: '',

            open() {
                this.show = true;
                this.monacoContent = '';
                this.errorMessage = '';
                this.$nextTick(() => this.$refs.jsonInput?.focus());
            },

            close() {
                if (this.isLoading) return;
                this.show = false;
            },

            submit() {
                if (this.isLoading) return;
                this.isLoading = true;
                this.errorMessage = '';

                try {
                    JSON.parse(this.monacoContent);
                } catch (e) {
                    this.errorMessage = 'Invalid JSON format.';
                    this.isLoading = false;
                    return;
                }

                this.$wire.call('apiCreateTranslation', {
                    appId: this.appId,
                    languageId: this.languageId,
                    namespace: this.namespace || null,
                    jsonText: this.monacoContent
                })
                .then(result => {
                    if (result && result.success) {
                        this.close();
                        this.show = false;
                    } else {
                        this.errorMessage = result?.message || 'API call failed.';
                    }
                })
                .catch(() => {
                    this.errorMessage = 'A network or server error occurred.';
                })
                .finally(() => {
                    this.isLoading = false;
                });
            }
        }"
        x-show="show"
        @open-api-create-modal.window="open()"
        x-on:keydown.escape.window="close()"
        class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none;"
    >
        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="show" x-transition.opacity class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div x-show="show"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                @click.away="close()"
                class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg dark:bg-gray-800"
            >
                <div class="flex items-center justify-between pb-3 border-b dark:border-gray-700">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                        API Create Translation
                    </h3>
                    <button @click="close()" type="button" :disabled="isLoading"
                        class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 disabled:opacity-50">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                </div>

                <form @submit.prevent="submit()">
                    <div class="mt-4 space-y-4">
                        <template x-if="errorMessage">
                            <div class="p-3 text-sm text-red-700 bg-red-100 rounded dark:bg-red-200 dark:text-red-800" x-text="errorMessage"></div>
                        </template>
                        <div>
                            <label for="appId" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{!! t('core.app') !!}</label>
                            <select wire:model.live="appId" id="appId"
                                wire:loading.attr="disabled"
                                :disabled="isLoading"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="">{!! t('core.all_apps_or_select_an_app') !!}</option>
                                <option value="0">{!! t('core.commons') !!}</option>
                                @foreach ($apps as $app)
                                    <option value="{{ $app['id'] }}">{{ $app['name'] }}</option>
                                @endforeach
                            </select>
                        </div>

                        <x-forms.language-select
                            :options="$languageOptions"
                            wire:model.live="languageId"
                            :label="t('core.language')"
                            :placeholder="t('core.choose_language')"
                            id="languageId"
                            wire:loading.attr="disabled"
                         />

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-200">Namespace (optional)</label>
                            <input type="text" x-model="namespace" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" :disabled="isLoading">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                                JSON Text
                            </label>
                            <textarea
                                x-model="monacoContent"
                                rows="10"
                                class="block w-full rounded-md border-gray-300 shadow-sm sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                placeholder='{
    "ui_no": "No",
    "core": {
        "no": "No",
        "address": {
            "zip": "Zip",
            "city": "City"
        }
    }
}'
                                :disabled="isLoading"
                            ></textarea>
                        </div>
                    </div>
                    <div class="mt-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" :disabled="isLoading"
                            class="inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto dark:bg-primary-500 dark:hover:bg-primary-600 disabled:opacity-75 disabled:cursor-not-allowed">
                            <svg x-show="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                            <span x-show="isLoading">Processing...</span>
                            <span x-show="!isLoading">Submit</span>
                        </button>
                        <button @click="close()" type="button" :disabled="isLoading"
                            class="inline-flex justify-center w-full px-4 py-2 mt-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 disabled:opacity-50">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{-- Batch Create End --}}
    

    @include('components.confirmation-modal')

</div>
