@props([
    'currentPlan' => null,
])

@if($currentPlan)
<div class="w-full">
    @php
        $canAddDiscount = $canAddDiscount ?? true;
        $isTrialSkipped = $isTrialSkipped ?? false;
    @endphp

    
    <div class="rounded-2xl border border-natural-300 overflow-hidden p-6">
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-2">
                {{ __('Current') }}
            </h2>
            <p class="text-sm text-gray-600">
                {{ __('Your active subscription') }}
            </p>
        </div>

        <div class="flex flex-row gap-3">
            
            <div class="flex flex-col gap-1">
                <span class="text-3xl font-semibold flex flex-row gap-2 flex-wrap">
                    <span class="py-1">
                        {{ $currentPlan->product->name }}
                    </span>
                    @if (!$isTrialSkipped && $currentPlan->has_trial)
                        <span class="text-xs rounded-full border border-primary-500 text-primary-500 px-2 md:px-4 font-semibold py-1 inline-block self-center">
                            {{ $currentPlan->trial_interval_count }} {{ $currentPlan->trialInterval()->firstOrFail()->name }} {{ __(' free trial included') }}
                        </span>
                    @endif
                </span>
                @if ($currentPlan->interval_count > 1)
                    <span class="text-xs">{{ $currentPlan->interval_count }} {{ mb_convert_case($currentPlan->interval->name, MB_CASE_TITLE, 'UTF-8') }}</span>
                @else
                    <span class="text-xs">{{ mb_convert_case($currentPlan->interval->adverb, MB_CASE_TITLE, 'UTF-8') }} {{ __('subscription.') }}</span>
                @endif

                <span class="text-xs">
                    {{ __('Starts immediately.') }}
                </span>

            </div>
        </div>

        <div class="text-primary-900 my-4">
            {{ __('What you get:') }}
        </div>
        <div>
            <ul class="flex flex-col items-start gap-3">
                @if ($currentPlan->product->features)
                    @foreach($currentPlan->product->features as $feature)
                        <li class="inline-flex gap-2 text-sm">
                            <div class="flex-shrink-0">
                                @if($feature['pivot']['enabled'])
                                    <svg class="w-5 h-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                            <span>{{ $feature['name'] }}</span>
                        </li>
                    @endforeach
                @endif
            </ul>
        </div>
        <hr class="my-6">
        <div class="flex flex-row justify-between">
            <div class="text-black-500 text-xl font-bold">
                {{ __('Price') }}
            </div>
            <div class="text-black-500 text-xl font-bold">
                @money($currentPlan->prices[0]->price, "eur")
            </div>
        </div>
    </div>


</div>
@endif
