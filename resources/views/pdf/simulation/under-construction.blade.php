<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title ?? 'Simulation PDF' }}</title>
    <style>
        body { font-family: 'Roboto Condensed', DejaVu Sans, sans-serif; margin: 40px; }
        h1 { color: #2d3748; }
        .meta { margin-bottom: 20px; }
        .meta span { display: inline-block; margin-right: 20px; font-weight: bold; }
        .content {
            margin-top: 30px;
            min-height: 80vh;
            width: 100%;
            text-align: center;
        }
        .content img {
            width: 250px;
            display: block;
            margin: 40px auto 40px auto;
        }
        .title {
            font-weight: 700;
            text-transform: uppercase;
            color: #2b6cb0;
            font-size: 2.5rem;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <h1>{{ $title ?? 'Simulation PDF' }}</h1>
    <div class="meta">
        <span>User: {{ $user ?? '' }}</span>
        <span>Date: {{ $date ?? '' }}</span>
    </div>
    <div class="content">
        <img
            src="https://cdn.solar-control.energy/images/static/under-construction.png"
            alt="Under Construction">
        <div class="title">
            {{ t('core.under_construction', [], $lang) }}
        </div>
    </div>
</body>
</html>
