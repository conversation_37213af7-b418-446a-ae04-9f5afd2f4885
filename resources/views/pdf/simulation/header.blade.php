<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>
    Simulation PDF
</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  {{-- STYLES --}}
  @include('pdf.css.header')
  @include('pdf.css.global')
  @include('pdf.css.font')
</head>

<body class="p-6">
  <header class="fixed h-fit top-0 w-full left-0">
    <div class="header-content">
      <img src="https://cdn.solar-control.energy/images/pdf/header-pdf.png" alt="Image PVGIS" class="top-img">
      <div class="header-text">
         <h2 data-ts="pvgis.simulation.pdf.header">
            DOSSIER TECHNIQUE ET FINANCIER SOLAIRE
          </h2>
      </div>
    </div>
  </header>


  <section class="bg-white">
    <div class="wrapper">
      <div class="left-section">
        {{-- <div class="content">
          <h3 data-ts="pvgis.simulation.pdf.self.consumption.header">AUTOCONSOMMATION </h3>
           <h3 data-ts="pvgis.simulation.pdf_autonomy" >+ AUTONOMIE </h3>
           <h3><span style=" font-family:  var(--font-condensed) !important;"  data-ts="pvgis.simulation.pdf.power" >PUISSANCE</span> {{getBatteryCapacity afs}} kWh</h3>
           <h3><span style=" font-family:  var(--font-condensed) !important;"  data-ts="pvgis.simulation.pdf.financing" >FINANCEMENT</span> <span class="uppercase"><span data-ts="pvgis.extranet.file_info.{{getFinancing afs}}">{{getFinancing afs}}</span></span></h3>

        </div> --}}
      </div>
      <div class="right-section">
         <div class="letter-container">
          <div class="letter"> <span data-ts="pvgis.simulation.pdf.client.name">NOM DU CLIENT</span></div>
          <div class="text uppercase ">{{ $client['name'] ?? '' }}</div>
        </div>
         <div class="letter-container " >
          <div class="letter">
              <span data-ts="pvgis.simulation.pdf.project.location">EMPLACEMENT DU PROJET</span>
          </div>
          <div class="text uppercase">{{ $projectInfo['location'] ?? '' }}</div>
        </div>
        <div class="letter-container">
          <div class="letter">
             <span data-ts="pvgis.simulation.pdf.solar.file">DOSSIER SOLAIRE</span>
          </div>
          <div class="text uppercase">{{ $projectInfo['projectName'] ?? '' }}</div>
        </div>
        <div class="letter-container letter-container-last">
          <div class="letter">
             <span data-ts="pvgis.simulation.pdf.simulation">SIMULATION</span>
          </div>
          <div class="text uppercase">{{ $projectInfo['simulationName'] ?? '' }}</div>
        </div>


      </div>
    </div>

     <div class="wrapper bottom-section">
      <div class="letter"></div>
      <div class="right-section">
        <div class="letter-container">
          <div class="letter">
              <span data-ts="pvgis.simulation.pdf.client.contact">CONTACT</span>
          </div>
          <div class="text">{{ $client['contact'] ?? '' }}</div>
        </div>
        <div class="letter-container">
          <div class="letter">
               <span data-ts="pvgis.simulation.pdf.client.email">E-MAIL</span>
          </div>
          <div class="text">{{ $client['email'] ?? '' }}</div>
        </div>
        <div class="letter-container">
          <div class="letter">
             <span data-ts="pvgis.simulation.pdf.client.phone">TÉLÉPHONE</span>
          </div>
         <div class="text">{{ $client['phoneNumber'] ?? '' }}</div>
        </div>
      </div>
    </div>


  </section>

  <footer class="fixed bottom-0 w-full left-0">
    <div class="flex w-full justify-between items-center">
      <span class="user-logo" style="margin-right: 20px;">
        @if (isset($urlImage))
            <img src="{{$urlImage}}" alt="Solar Center Mauritius Logo"
            class="footer-logo" />
        @endif
      </span>
    </div>
  </footer>
</body>

</html>
