<style>
    :root {
        --color-info: #1b69a7;
    }

    body {
        font-family: var(--font-helvetica-neue-light);
        color: var(--color-info);
        margin: 0;
        padding: 0;
    }

    .container {
        margin: 0 auto;
        margin-left: 30px !important;
    }

    .main-container {
        margin: 0 auto;
        margin-right: 30px !important;
    }

    .flex-container {
        display: flex;
    }

    .header {
        font-size: 24px;
        color: #1b69a7;
        position: fixed;
        top: 0;
        width: 100%;
        padding-top: 20px;
    }

    .header h2 {
        display: flex;
        align-items: center;

        font-family: var(--font-bold-condensed);
        color: #1b69a7;
        font-size: 1.55em;
        font-weight: 300;
        line-height: 1.2;
    }

    .header p {
        font-size: 12px;
        color: black;
    }

    .header h2 img {
        margin-right: 10px;
    }

    .content {
        display: flex;
        margin-top: 100px;
    }

    .main-content {
        margin-top: 100px !important;
    }

    .left-column {
        padding-left: 20px;
        margin-top: 60px;
        width: 80%;
        /* Add this line */
    }

    .full-column {
        margin-top: 120px;
        width: 100%;
    }

    .right-column {
        padding: 20px;
        margin-top: 30px;
    }

    .additional-column {
        width: 120px;
        display: flex;
        margin-right: 20px;
    }

    .property {
        display: flex;
        margin-bottom: 20px;
    }

    .property-image {
        margin-right: 6px;
    }

    .property-image-header {
        margin-right: 20px;
        background-color: rgb(0, 105, 179);
        margin-left: 5px;
    }

    .property-info {
        flex: 1;
        line-height: 1em;
        font-family: Arial, sans-serif;
    }

    .property-info h2 {
        margin: 0;
        color: #6787ba;
        font-family: var(--font-bold-condensed);
        font-size: 25px;
        line-height: 1.2em;
    }

    .property-info p {
        margin: 5px 0;
        font-family: var(--font-helvetica-neue-light);
        font-size: 18px;
    }

    .property-info h1 {
        margin: 5px 0;
        font-family: var(--font-condensed);
        font-size: 17px;
    }

    .financing {
        margin-top: 140px;
        margin-right: 5px;
    }

    .consumption {
        margin-top: 90px;
        margin-right: 20px;
    }

    .investment {
        margin-top: 155px;
        margin-right: 20px;
    }

    .left-image {
        margin-right: 20px;
    }

    .footer-logo {
        height: 105px;
        width: 105px;
        position: fixed;
        bottom: 20px;
        left: 15px;
    }

    .user-logo {
        height: 105px;
        width: 105px;
    }

    .user-logo img {
        object-fit: cover;
    }

    h2 {
        margin-top: 0px;
        line-height: 1em;
    }

    img {
        max-width: 100%;
        height: auto;
    }

    @media print {
        .property-image-header img {
            width: 105px;
            height: auto;
        }
    }

    .value {
        margin-left: 100px;
    }

    .value-0 {
        margin-left: 0px;
    }

    .value-20 {
        margin-left: -50px;
    }

    .num-page {
        color: white;
        height: 60px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: var(--font-bold-condensed);
    }

    .block-app-footer {
        position: fixed;
        bottom: 10px;
        left: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        width: 90px;
    }

    .image-company-footer {
        object-fit: cover;
        width: 90px;
        height: 90px;
    }

    .logo-app-footer {
        background-color: rgb(0, 105, 179);
        padding-bottom: 5px;
        padding-top: 10px;
        padding-inline: 18px;
        color: #fff;
        text-align: center;
        font-size: 14px;
        font-family: var(--font-condensed) !important;
        text-wrap: nowrap;
    }


    .information-text {
        font-family: var(--font-condensed);
        text-align: left;
        font-size: 13px;
        color: var(--color-info);
        margin-bottom: 20px;
        line-height: 1.3em;
        letter-spacing: 0.75px;
    }


    .legend-x {
        position: absolute;
        bottom: -10px;
        left: 51%;
        transform: translateX(-50%);
        color: rgb(0, 0, 0) !important;
        white-space: nowrap;
        font-size: 14px;
        var(--font-condensed);
    }

    .legend-y {
        position: absolute;
        top: 43%;
        left: -20px;
        transform: rotate(-90deg);
        color: rgb(0, 0, 0) !important;
        white-space: nowrap;
        font-size: 14px;
        font-style: var(--font-condensed);
    }
</style>
