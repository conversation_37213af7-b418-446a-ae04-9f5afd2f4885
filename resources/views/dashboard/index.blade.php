<x-layouts.dashboard :styles="$styles">
    <x-slot name="content">
        {{-- ───────────── Alpine store initialisation --}}
        <div x-data="dashboardTable({{ json_encode($user) }},{{ json_encode($subscription) }},)"
            class="mx-auto py-6 space-y-8">
            <!-- TOP BAR ====================================================== -->
            <div class="flex flex-col md:flex-row md:items-stretch md:justify-between gap-6">
                <div class="flex flex-col justify-between">
                    <h1 class="text-2xl md:text-3xl font-extrabold text-primary_ui-50">
                        Welcome {{ Str::upper(auth()->user()->getPublicName() ?? 'USER') }}
                    </h1>
                    <x-button-link.primary-ui
                        class="inline-block mt-6 !w-[220px] rounded-sm uppercase !text-lg !font-bold"
                        href="{{ route('dashboard.create-project') }}">
                        {{ t('core.project.create') }}
                    </x-button-link.primary-ui>
                </div>

                @if (!empty($subscription))
                    <ul class="text-md text-gray-700 italic space-y-0.5 md:text-left leading-5">
                        <li><span class="font-light">Subscription :</span> {{ $subscription['plan'] }}</li>
                        <li><span class="font-light">Users :</span> {{ $subscription['users'] }}</li>
                        <li><span class="font-light">Subscription date :</span> {{ \Carbon\Carbon::parse($subscription['since'])->format('d/m/Y') }}</li>
                        <li><span class="font-light">Number of projects created :</span>
                            {{ $subscription['projectsTotal'] }}</li>
                        <li><span class="font-light">Credit projects Year :</span> {{ $subscription['credits'] }}
                        </li>
                        <li><span class="font-light">Credit projects Month :</span>
                            {{ floor($subscription['credits'] / 12) }}
                        </li>
                        <li><span class="font-light">Projects created Month :</span> {{ $subscription['projectsMonth'] }}
                        </li>
                        <li><span class="font-light">Project balance :</span> {{ $subscription['balanceMonth'] }}
                        </li>
                    </ul>
                @else
                    <div class="text-md text-red-600 md:text-right leading-5">
                        <p>No active subscription.</p>
                        <a href="/subscription"
                            class="inline-block mt-2 px-4 py-2 bg-primary_ui-50 text-white text-sm font-semibold rounded hover:bg-primary_ui-60">
                            Subscribe Now
                        </a>
                    </div>
                @endif


            </div>

            <!-- PROJECT MANAGEMENT CARD ===================================== -->
            <div class="border-2 border-primary_ui-50 rounded-sm overflow-hidden">
                <!-- header -->
                <div class="px-6 py-4 border-b border-primary_ui-50">
                    <h2 class="text-lg font-semibold text-primary_ui-50">Project management</h2>
                </div>

                <!-- FILTER BAR -->
                <div class="px-4 py-4 flex text-black gap-2 text-md w-full">
                    <div class="w-[10%]"></div>
                    <div class="w-[23%]">
                        <label class="block font-medium mb-1">Projects</label>

                        <div class="relative">
                            <!-- Trigger Button -->
                            <button type="button" class="w-full capitalize input-filter" @click="dropdownOpen = !dropdownOpen">
                                <!-- Project name or default text -->
                                <span x-text="selectedProjectName || 'All projects (A-Z)'"></span>

                                <!-- Caret Icon (Chevron Down) -->
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 bg-gray-400 rounded font-bold h-4 ml-2 transition-transform duration-200 text-white"
                                    :class="{ 'rotate-180': dropdownOpen }" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.23 7.21a.75.75 0 011.06.02L10 11.293l3.71-4.06a.75.75 0 111.08 1.04l-4.25 4.65a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- Dropdown -->
                            <div x-show="dropdownOpen" @click.away="dropdownOpen = false"
                                class="absolute z-50 mt-1 w-full bg-gray-100 border shadow max-h-60 overflow-y-auto">
                                <!-- Search Input -->
                                <div class="px-2 py-2 border-b">
                                    <input type="text" class="w-full input-filter" placeholder="Search..."
                                        x-model="filters.project_name" @input="search">
                                </div>

                                <!-- Options -->
                                <ul>
                                    <li>
                                        <button type="button" class="w-full text-left px-3 py-2 hover:bg-gray-100"
                                            @click="selectProject('', 'All projects (A-Z)')">All projects (A-Z)</button>
                                    </li>

                                    <!-- Loading state -->
                                    <template x-if="searching">
                                        <li
                                            class="px-3 py-4 text-center text-gray-500 text-md italic flex flex-col items-center">
                                            <!-- Spinner icon -->
                                            <svg class="animate-spin h-5 w-5 text-gray-400 mb-1"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor"
                                                    d="M4 12a8 8 0 018-8v4l3.5-3.5A10 10 0 002 12h2z"></path>
                                            </svg>
                                            Loading...
                                        </li>
                                    </template>

                                    <!-- Results -->
                                    <template x-if="!searching && searchResult.length > 0">
                                        <template x-for="p in searchResult" :key="'plist_' + p.id">
                                            <li>
                                                <button type="button"
                                                    class="w-full text-left px-3 py-2 hover:bg-gray-50 ease-in"
                                                    @click="selectProject(p.id, p.project_name)"
                                                    x-text="p.project_name"></button>
                                            </li>
                                        </template>
                                    </template>

                                    <!-- No results -->
                                    <template x-if="!searching && searchResult.length === 0 && filters.project_name">
                                        <li
                                            class="px-3 py-4 text-center text-gray-400 italic flex flex-col items-center">
                                            <!-- Heroicon: Folder -->
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300 mb-1"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 7a2 2 0 012-2h4l2 2h8a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2V7z" />
                                            </svg>
                                            No projects found.
                                        </li>
                                    </template>
                                </ul>


                            </div>
                        </div>
                    </div>


                    <div class="w-[10%] relative">
                        <label class="block font-medium mb-1">Profile</label>

                        <div class="relative">
                            <!-- Trigger Button -->
                            <button type="button" class="w-full capitalize input-filter flex items-center justify-between"
                                @click="profileDropdownOpen = !profileDropdownOpen">
                                <span x-text="selectedProfileName || 'All profiles'"></span>

                                <!-- Chevron Icon -->
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 h-4 bg-gray-400 rounded text-white ml-2 transition-transform duration-200"
                                    :class="{ 'rotate-180': profileDropdownOpen }" fill="currentColor"
                                    viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.23 7.21a.75.75 0 011.06.02L10 11.293l3.71-4.06a.75.75 0 111.08 1.04l-4.25 4.65a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- Dropdown -->
                            <div x-show="profileDropdownOpen" @click.away="profileDropdownOpen = false"
                                class="absolute z-50 mt-1 w-full bg-gray-100 border shadow rounded max-h-60 overflow-y-auto">
                                <ul>
                                    <li>
                                        <button type="button" class="w-full text-left px-3 py-2 hover:bg-gray-50"
                                            @click="filters.project_type = ''; selectedProfileName = 'All profiles'; profileDropdownOpen = false; applyFilters()">
                                            All profiles
                                        </button>
                                    </li>

                                    <template x-for="t in allType" :key="t">
                                        <li>
                                            <button type="button"
                                                class="w-full text-left px-3 py-2 hover:bg-gray-50 capitalize"
                                                @click="filters.project_type = t; selectedProfileName = t; profileDropdownOpen = false; applyFilters()"
                                                x-text="t">
                                            </button>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>



                    <!-- Creation Date -->
                    <div class="w-[11%]">
                        <label for="created_at" class="block font-medium mb-1">Creation date</label>
                        <div class="relative">
                            <input type="date" id="created_at"
                                class="w-full input-filter uppercase appearance-none pl-7 placeholder-gray-400/50"
                                placeholder="Creation date" x-model="filters.created_at" @change="applyFilters" />
                            <div
                                class="absolute left-2 top-1/2 -translate-y-1/2 w-5 h-5 flex items-center justify-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                    class="w-4 h-4 text-gray-500">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM18 9H2v6a2 2 0 002 2h12a2 2 0 002-2V9z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Date Modified -->
                    <div class="w-[11%]">
                        <label for="updated_at" class="block font-medium mb-1">Date modified</label>
                        <div class="relative">
                            <input type="date" id="updated_at"
                                class="w-full input-filter uppercase appearance-none pl-7 placeholder-gray-400/50"
                                placeholder="Date modified" x-model="filters.updated_at" @change="applyFilters" />
                            <div
                                class="absolute left-2 top-1/2 -translate-y-1/2 w-5 h-5 flex items-center justify-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                    class="w-4 h-4 text-gray-500">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM18 9H2v6a2 2 0 002 2h12a2 2 0 002-2V9z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="w-[25%] relative"
                        x-data="{ simDropdownOpen: false, selectedSimName: 'All simulations' }">
                        <label class="block font-medium mb-1">Simulations</label>
                        <div class="relative">
                            <!-- Trigger Button -->
                            <button type="button" class="w-full capitalize input-filter flex items-center justify-between"
                                @click="simDropdownOpen = !simDropdownOpen">
                                <span x-text="selectedSimName"></span>
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 h-4 bg-gray-400 rounded text-white ml-2 transition-transform duration-200"
                                    :class="{ 'rotate-180': simDropdownOpen }" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.23 7.21a.75.75 0 011.06.02L10 11.293l3.71-4.06a.75.75 0 111.08 1.04l-4.25 4.65a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- Dropdown List -->
                            <div x-show="simDropdownOpen" @click.away="simDropdownOpen = false"
                                class="absolute z-50 mt-1 w-full bg-gray-100 border shadow rounded max-h-60 overflow-y-auto">
                                <ul>
                                    <li>
                                        <button type="button" class="w-full text-left px-3 py-2 hover:bg-gray-50"
                                            @click="
                            filters.project_item_type = '';
                            selectedSimName = 'All simulations';
                            simDropdownOpen = false;
                            applyFilters();
                        ">
                                            All simulations
                                        </button>
                                    </li>
                                    <template x-for="iType in allItemType" :key="iType">
                                        <li>
                                            <button type="button"
                                                class="w-full text-left px-3 py-2 hover:bg-gray-50 capitalize" @click="
                                filters.project_item_type = iType;
                                selectedSimName = iType;
                                simDropdownOpen = false;
                                applyFilters();
                            " x-text="iType">
                                            </button>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="w-[10%] relative" x-data="{ userDropdownOpen: false }">
                        <label class="block font-medium mb-1">All users</label>
                        <div class="relative">
                            <button type="button" class="w-full capitalize input-filter flex items-center justify-between"
                                @click="userDropdownOpen = !userDropdownOpen">
                                <span>All users</span>
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 h-4 bg-gray-400 rounded text-white ml-2 transition-transform duration-200"
                                    :class="{ 'rotate-180': userDropdownOpen }" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.23 7.21a.75.75 0 011.06.02L10 11.293l3.71-4.06a.75.75 0 111.08 1.04l-4.25 4.65a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-show="userDropdownOpen" @click.away="userDropdownOpen = false"
                                class="absolute z-50 mt-1 w-full bg-gray-100 border shadow rounded max-h-60 overflow-y-auto">
                                <ul>
                                    <li>
                                        <button type="button" class="w-full text-left px-3 py-2 hover:bg-gray-50"
                                            @click="userDropdownOpen = false">
                                            All users
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- TABLE -->
                <div class="overflow-x-auto">
                    <table class="project-table min-w-full divide-y divide-gray-300 text-md">
                        <thead class="bg-primary_ui-50/5 text-gray-800">
                            <tr>
                                <th class="w-[10%] px-4 py-2 !text-center font-normal">Action</th>

                                <th class="w-[23%] px-4 py-2 cursor-pointer font-normal whitespace-nowrap hover:bg-primary_ui-50/10"
                                    @click="toggleSort('project_name')">
                                    Projects
                                    <span x-text="sortLabel('project_name')"></span>
                                </th>

                                <th class="w-[10%] px-4 py-2 cursor-pointer font-normal whitespace-nowrap hover:bg-primary_ui-50/10"
                                    @click="toggleSort('project_type')">
                                    Profile
                                    <span x-text="sortLabel('project_type')"></span>
                                </th>

                                <th class="w-[11%] px-4 py-2 font-normal">
                                    Creation date
                                </th>

                                <th class="w-[11%] px-4 py-2 font-normal">
                                    Date modified
                                </th>

                                <th class="w-[35%] px-4 py-2 font-normal">Simulations performed</th>
                            </tr>
                        </thead>

                        <tbody class="divide-y divide-gray-200 text-black align-top">

                            <template x-for="(project, index) in original" :key="index + '_p' + project.id">
                                <template x-data="{ hover: false }" x-for="(item, index) in getFor(project)"
                                    :key="'item_' + item.id">
                                    <tr class="group hover:bg-[#f7fdff]" @mouseenter="hover = true"
                                        @mouseleave="hover = false">

                                        <!-- ACTION ICONS -->
                                        <td class="px-4 py-3 text-center" x-show="index == 0"
                                            :rowspan="getRowSpan(project)" :class="{ 'bg-[#f7fdff]': hover }">
                                            <div class="mt-3">
                                                <div class="flex items-center justify-center gap-4">
                                                    <!-- chart / view -->
                                                    <a :href="`/project-info/${project.id}`"
                                                        class="hover:opacity-80 transition-opacity"
                                                        title="View statistics">
                                                        <img src="{{ asset('images/dashboard/graph-icone.png') }}"
                                                            alt="Graph" class="w-5 h-5">
                                                    </a>

                                                    <!-- delete -->
                                                    <button class="hover:opacity-80 transition-opacity" title="Delete"
                                                        @click="openDeleteModal(project)">
                                                        <img src="{{ asset('images/dashboard/corbeille.jpg') }}"
                                                            alt="Delete" class="w-5 h-5">
                                                    </button>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- NAME + LOCATION (placeholder) -->
                                        <td class="px-4 py-3 whitespace-nowrap" x-show="index == 0"
                                            :rowspan="getRowSpan(project)" :class="{ 'bg-[#f7fdff]': hover }"
                                            x-data="{ showDetails: false }">

                                            <!-- Project name -->
                                            <a :href="`/project-info/${project.id}`"
                                                class="hover:opacity-80 hover:underline decoration-black decoration-2 transition-opacity">
                                                <span class="font-semibold capitalize text-black text-xl"
                                                    x-text="project.project_name"></span>
                                            </a>

                                            <!-- Address -->
                                            <div class="text-sm leading-4 font-normal text-gray-900">
                                                <span x-html="formatAddress(project)"></span>

                                                <!-- Show/Hide Details block with animation -->
                                                <div x-show="showDetails" class="mt-2">
                                                    <div>• <span class="font-medium">Creation date:</span>
                                                        <span x-text="formatDate(project.created_at)"></span>
                                                    </div>
                                                    <div>• <span class="font-medium">Modification date:</span>
                                                        <span x-text="formatDate(project.updated_at)"></span>
                                                    </div>
                                                </div>

                                                <!-- Toggle Link -->
                                                <div class="mt-2">
                                                    <button
                                                        @click="showDetails = !showDetails; if (!showDetails) hover = false"
                                                        class="text-primary_ui-50 hover:underline text-sm focus:outline-none">
                                                        <span
                                                            x-text="showDetails ? 'Hide details ▲' : 'Show details ▼'"></span>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>


                                        <td class="px-4 py-4 capitalize font-normal text-black text-md"
                                            x-show="index == 0" :rowspan="getRowSpan(project)"
                                            :class="{ 'bg-[#f7fdff]': hover }" x-text="project.project_type"></td>

                                        <td class="px-4 py-3" x-text="formatDate(item.created_at)"></td>
                                        <td class="px-4 py-3" x-text="formatDate(item.updated_at)"></td>

                                        <td class="px-4 py-3">
                                            <template x-if="item.id > 0">
                                                <div class="pb-1 mb-2 border-b">
                                                    <div class="font-semibold capitalize text-primary_ui-50"
                                                        x-text="item.item_name"></div>
                                                    <div class="text-sm text-gray-600 capitalize"
                                                        x-text="item.item_type">
                                                    </div>
                                                    <div class="text-sm mt-1">
                                                        <a :href="`/project-items/${item.id}`"
                                                            class="text-primary_ui-50 underline">Show
                                                            details</a>
                                                    </div>
                                                </div>
                                            </template>

                                            <template x-if="item.id == 0">
                                                <div class="pb-1 mb-2 border-b">
                                                    <div class="text-sm mt-1">
                                                        <a :href="`/project-items`"
                                                            class="text-primary_ui-50 underline">Create simulation</a>
                                                    </div>
                                                </div>
                                            </template>

                                            <template
                                                x-if="(!isExpanded(project.id) && index == 2) || (isExpanded(project.id) && index + 1 == project.items.length)">
                                                <div class="text-sm mt-1"
                                                    x-show="project.items && project.items.length > 3">
                                                    <a href="#" @click.prevent="toggleShowAll(project.id)"
                                                        class="text-primary_ui-50 text-base hover:underline">
                                                        <span
                                                            x-text="isExpanded(project.id) ? 'Show less ▲' : 'Show all ▼'"></span>
                                                    </a>
                                                </div>
                                            </template>
                                        </td>
                                    </tr>
                                </template>
                            </template>


                            <!-- Empty State -->
                            <template x-if="!isLoading && original.length == 0">
                                <tr>
                                    <td colspan="6" class="py-12">
                                        <div class="flex flex-col items-center justify-center text-gray-500">
                                            <!-- Heroicon: Folder Icon -->
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-4 text-gray-400"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 7a2 2 0 012-2h4l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2V7z" />
                                            </svg>
                                            <p class="text-lg font-medium">No Projects</p>
                                        </div>
                                    </td>
                                </tr>
                            </template>

                            <!-- Loading State -->
                            <template x-if="isLoading">
                                <tr>
                                    <td colspan="6" class="py-12">
                                        <div class="flex flex-col items-center justify-center text-gray-500">
                                            <!-- Heroicon-like Spinner -->
                                            <svg class="animate-spin h-10 w-10 text-gray-400 mb-4"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor"
                                                    d="M4 12a8 8 0 018-8v4l3.536-3.536A9.972 9.972 0 002 12h2z"></path>
                                            </svg>
                                            <p class="text-lg font-medium">Loading Projects...</p>
                                            <p class="text-md text-gray-400">Please wait while we fetch your data.</p>
                                        </div>
                                    </td>
                                </tr>
                            </template>

                        </tbody>
                    </table>
                </div>

                <div class="flex justify-end items-center gap-4 mt-6">
                    <button @click="prevPage" :disabled="page === 1"
                        class="btn rounded-none bg-primary_ui_high-50 hover:bg-primary_ui-50 text-white">Previous</button>
                    <span x-text="`Page ${page} of ${totalPages}`"></span>
                    <button @click="nextPage" :disabled="page === totalPages"
                        class="btn rounded-none bg-primary_ui_high-50 hover:bg-primary_ui-50 text-white">Next</button>
                </div>
            </div>

            <!-- CONFIRM DELETE MODAL ========================================= -->
            <x-modal-confirm show="showConfirmModal" onConfirm="confirmDelete()">
                <template x-if="selected">
                    <span>
                        {{ t('core.project.delete_confirmation') }}<br>
                        <b x-text="selected.name"></b>
                    </span>
                </template>
            </x-modal-confirm>
        </div>

        {{-- ─────────────────────—— Alpine component (inline for brevity) --}}
        <script>
            window.APP_URL = @json(config('services.app.url'));
        </script>
        
        @vite('resources/js/project/dashboard.js')

    </x-slot>
</x-layouts.dashboard>
