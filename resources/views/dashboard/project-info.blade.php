<x-layouts.dashboard :styles="$styles">
    <x-slot name="content">
        <div
            class="project-info"
            x-data="{
                latitude: {{ isset($project) ? $project->latitude : '-18.9185' }},
                longitude: {{ isset($project) ? $project->longitude : '47.5211' }},
                locationType: '{{ isset($project) ? $project->location_type : 'address' }}',
                address: {
                    country: '{{ isset($project) ? $project->country : '' }}',
                    city:    '{{ isset($project) ? $project->city : '' }}',
                    street:  '{{ isset($project) ? $project->street : '' }}',
                    number:  '{{ isset($project) ? $project->street_number : '' }}'
                },
                isLoading: false,
                profile: '{{ isset($project) ? $project->project_type : 'residential' }}',
                project_name: '{{ isset($project) ? $project->project_name : '' }}',
                project_info: '{{ isset($project) ? $project->project_info : '' }}',
                country_id: '{{ isset($project) ? $project->country_id : '' }}',
                subscription_id: '{{ $subscription['id'] ?? '' }}',
                error: '',
                success: '',
                showConfirmationScreen: false,
                validation: { project_info: '', project_name: '' },
                dirty: { project_info: false, project_name: false },
                fieldsDisabled: {{ isset($project) ? 'true' : 'false' }},
                isInfo: {{ isset($isInfo) && $isInfo === 'true' ? 'true' : 'false' }},
                showGeolocationInformation: {{ isset($project) ? 'false' : 'true' }},

                async init() {
                    if (!this.fieldsDisabled) {
                        const ip = '{{ getClientIpAddress() }}';
                        await this.updateAddressFromIp(ip);

                        this.$watch('locationType', async (newType, oldType) => {
                            // 1) map locationType → savedStates key
                            const keyMap = {
                                'address':        'address',
                                'gps-point':      'gpsPoint',
                                'gps-geolocated': 'gpsGeolocated'
                            };
                            const oldKey = keyMap[oldType];
                            const newKey = keyMap[newType];

                            // 2) snapshot current state into old slot
                            this.savedStates[oldKey] = {
                                address:   { ...this.address },
                                latitude:  this.latitude,
                                longitude: this.longitude
                            };

                            // 3) restore if we have a saved slot
                            const slot = this.savedStates[newKey];
                            if (slot) {
                                this.address   = { ...slot.address };
                                this.latitude  = slot.latitude;
                                this.longitude = slot.longitude;
                            }
                            // 4) first‐time gps-geolocated: actually geolocate
                            else if (newType === 'gps-geolocated') {
                                await this.geolocateUser();
                            }

                            // 5) notify the map to jump to these coords
                            this.$dispatch('map-update', {
                                lat: this.latitude,
                                lng: this.longitude
                            });
                        });
                    }
                },

                async updateAddressFromIp(ip) {
                    const controller = new AbortController();
                    const signal = controller.signal;

                    try {
                        const response = await fetch(`/api/ip-location/${ip}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content
                            },
                            signal: signal
                        });

                        if (signal.aborted) return;
                        if (!response.ok) throw new Error('Network response was not ok');
                        const data = await response.json();

                        if (data?.country) this.address.country = data.country;
                        if (data?.city) this.address.city = data.city;
                        if (data?.street) this.address.street = data.street;
                        if (data?.number) this.address.number = data.number;
                        if (data?.lat) this.latitude = data.lat;
                        if (data?.lon) this.longitude = data.lon;
                    } catch (status) {
                        this.address = { country: '', city: '', street: '', number: '' };
                    }
                },

                async updateAddressFromCoords() {
                    const geocoder = new google.maps.Geocoder();
                    const latLng = new google.maps.LatLng(this.latitude, this.longitude);

                    try {
                        const results = await new Promise((resolve, reject) => {
                            geocoder.geocode({ 'location': latLng }, (res, status) => {
                                if (status === 'OK') resolve(res);
                                else reject(status);
                            });
                        });

                        if (results[0]) {
                            this.address = { country: '', city: '', street: '', number: '' };
                            results[0].address_components.forEach(component => {
                                if (component.types.includes('country')) this.address.country = component.long_name;
                                if (component.types.includes('locality')) this.address.city = component.long_name;
                                if (component.types.includes('route')) this.address.street = component.long_name;
                                if (component.types.includes('street_number')) this.address.number = component.long_name;
                            });
                        }
                    } catch (status) {
                        this.address = { country: '', city: '', street: '', number: '' };
                    }
                },

                async geolocateUser() {
                    if (!navigator.geolocation) return;

                    this.isLoading = true;

                    try {
                        const position = await new Promise((resolve, reject) => {
                            navigator.geolocation.getCurrentPosition(resolve, reject);
                        });

                        this.latitude = position.coords.latitude;
                        this.longitude = position.coords.longitude;
                        await this.updateAddressFromCoords();

                    } catch (error) {
                        // Optional: handle geolocation errors
                    } finally {
                        this.isLoading = false;
                    }
                },

                async geocodeAddress() {
                    const fullAddress = `${this.address.number} ${this.address.street}, ${this.address.city}, ${this.address.country}`.trim();

                    if (!fullAddress || fullAddress === ', ,' || (this.address.city === '' && this.address.street === '' && this.address.country === '')) {
                        this.latitude = -18.9185;
                        this.longitude = 47.5211;
                        this.isLoading = false;
                        return;
                    }

                    const requestTimestamp = Date.now();
                    this.lastGeocodeRequestTimestamp = requestTimestamp;
                    this.isLoading = true;

                    const geocoder = new google.maps.Geocoder();
                    try {
                        const results = await new Promise((resolve, reject) => {
                            geocoder.geocode({ 'address': fullAddress }, (res, status) => {
                                if (status === 'OK') resolve(res);
                                else reject(status);
                            });
                        });

                        if (this.lastGeocodeRequestTimestamp !== requestTimestamp) return;

                        if (results[0]) {
                            const location = results[0].geometry.location;
                            this.latitude = location.lat();
                            this.longitude = location.lng();
                        }
                    } catch (status) {
                        // Optional: handle geocode errors
                    } finally {
                        if (this.lastGeocodeRequestTimestamp === requestTimestamp) {
                            this.isLoading = false;
                        }
                    }
                },

                validateField(field) {
                    this.dirty[field] = true;
                    this.validateForm();
                },

                validateForm(markAllDirty) {
                    let valid = true;

                    if (markAllDirty) {
                        // Mark all as dirty so validation shows after submit attempt
                        this.dirty.project_info = true;
                        this.dirty.project_name = true;
                    }

                    // Validate fields
                    this.validation.project_info = !this.project_info && this.dirty.project_info ? 'This field is required.' : '';
                    this.validation.project_name = !this.project_name && this.dirty.project_name ? 'This field is required.' : '';

                    if (!this.project_info) valid = false;
                    if (!this.project_name) valid = false;
                    return valid;
                },

                async saveProject() {
                    this.isLoading = true;
                    this.error = '';
                    this.success = '';
                    const payload = {
                        project_name: this.project_name,
                        project_info: this.project_info,
                        project_type: this.profile,
                        location_type: this.locationType,
                        latitude: this.latitude,
                        longitude: this.longitude,
                        street_number: this.address.number,
                        street: this.address.street,
                        city: this.address.city,
                        country: this.address.country,
                        country_id: this.country_id,
                        subscription_id: this.subscription_id,
                    };
                    try {
                        const response = await fetch('/api/projects', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify(payload),
                        });
                        const data = await response.json();
                        if (!response.ok) {
                            this.error = data.message || (data.errors ? Object.values(data.errors).flat().join(', ') : 'Unknown error');
                            this.isLoading = false;
                            return;
                        }
                        this.success = 'Project created successfully!';
                        console.log(data)
                        window.location.href = '/project-info/' + data?.data?.id;
                    } catch (e) {
                        this.error = 'Failed to create project.';
                        this.isLoading = false;
                    } finally {
                        {{-- this.isLoading = false; --}}
                    }
                },
            }"
            x-init="
                init();
                $watch('locationType', async (value) => {
                    if (value === 'gps-geolocated') {
                        await geolocateUser();
                    }
                });
            "
        >
            {{-- ───────── Page Title --}}
            <h1 class="text-2xl font-bold text-primary_ui-50 mb-2">
                {{ isset($project) ? t('core.project.folder_overview') : t('core.project.creation') }}
            </h1>

            {{-- ───────── Map (placeholder) --}}
            <div class="w-full">
                <x-map.map-picker :distance-km="0.1" :editable="$isInfo === 'false'" mapClass="h-[450px]"></x-map.map-picker>
            </div>

            {{-- Section heading --}}
            <h2 class="text-primary_ui-50 text-2xl font-bold mb-4 mt-3">
                {{ t('core.geolocation') }}
            </h2>

            {{-- “Show Geolocation Info” small note --}}
            <p class="text-md text-gray-700 hover:underline -mt-3 mb-4 cursor-pointer w-max" @click="showGeolocationInformation = !showGeolocationInformation">
                {{ t('core.show_geolocation_info') }}
            </p>

            <!-- Show error/success -->
            <template x-if="error">
                <div class="text-red-600 m-0 font-semibold mb-2">
                    {{ t('core.project.creation_error') }}
                </div>
            </template>
            <template x-if="success">
                <div class="text-green-600 m-0 font-semibold mb-2">
                    {{ t('core.project.creation_success') }}
                </div>
            </template>

            {{-- CONTENT GRID --}}
            <form
                x-cloak
                x-show="showGeolocationInformation"
                method="POST"
                action="#"
                @submit.prevent="if (validateForm(true)) { showConfirmationScreen = true }"
                class="border-2 border-primary_ui-50 rounded-sm bg-white p-5 space-y-6 relative mb-8 min-h-[260px]"
            >
                <div class="flex gap-6">
                    <div class="flex-1 flex gap-6">
                        <fieldset class="location-type">
                            <legend class="font-semibold mb-2 text-lg text-primary_ui-50">
                                {{ t('core.production_location') }}
                            </legend>
                            <div class="space-y-2">
                                <x-forms.custom-radio size="md" model="locationType" disabled="{{ $isInfo }}" value="address">{{ t('core.location.address') }}</x-forms.custom-radio>
                                <x-forms.custom-radio size="md" model="locationType" disabled="{{ $isInfo }}" value="gps-point">{{ t('core.location.gps_point') }}</x-forms.custom-radio>
                                <x-forms.custom-radio size="md" model="locationType" disabled="{{ $isInfo }}" value="gps-geolocated">{{ t('core.location.gps_geolocated') }}</x-forms.custom-radio>
                            </div>
                        </fieldset>
                        <div class="flex-1 gap-10 flex text-black">
                            <table x-show="locationType === 'address' || locationType === 'gps-geolocated'" x-cloak class="location-coordinate mt-[28px] h-max">
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.country') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.country') }}"
                                            x-model="address.country"
                                            @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui"
                                            :disabled="fieldsDisabled"
                                        />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.city') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.city') }}"
                                            x-model="address.city"
                                            @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui"
                                            :disabled="fieldsDisabled"
                                        />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.street') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.street') }}"
                                            x-model="address.street"
                                            @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui"
                                            :disabled="fieldsDisabled"
                                        />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.number') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.number') }}"
                                            x-model="address.number"
                                            @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui"
                                            :disabled="fieldsDisabled"
                                        />
                                    </td>
                                </tr>
                            </table>
                            <table x-show="locationType === 'gps-point'" x-cloak class="location-coordinate mt-[28px] h-max">
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.latitude') }}</label></td>
                                    <td>
                                        <input type="number" step="any" placeholder="{{ t('core.form.latitude') }}"
                                            x-model.number="latitude"
                                            :disabled="fieldsDisabled || locationType === 'gps-geolocated'"
                                            class="input-ui" />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.longitude') }}</label></td>
                                    <td>
                                        <input type="number" step="any" placeholder="{{ t('core.form.longitude') }}"
                                            x-model.number="longitude"
                                            :disabled="fieldsDisabled || locationType === 'gps-geolocated'"
                                            class="input-ui" />
                                    </td>
                                </tr>
                            </table>
                            <div class="folder-information">
                                <div class="mb-2">
                                    <label class="form-label font-bold text-md text-primary_ui-50">{{ t('core.customer.name') }}</label>
                                    <input
                                        type="text"
                                        class="input-ui"
                                        :class="validation.project_info ? 'border-red-500 outline-none' : ''"
                                        x-model="project_info"
                                        @input="validateField('project_info')"
                                        :disabled="fieldsDisabled"
                                    >
                                    <template x-if="validation.project_info">
                                        <div class="text-red-500 text-md font-semibold text-[15px] mt-[-3px] mb-1" x-text="validation.project_info"></div>
                                    </template>
                                </div>
                                <div>
                                    <label class="form-label font-bold text-md text-primary_ui-50">{{ t('core.project.name') }}</label>
                                    <input
                                        type="text"
                                        class="input-ui"
                                        :class="validation.project_name ? 'border-red-500 outline-none' : ''"
                                        x-model="project_name"
                                        @input="validateField('project_name')"
                                        :disabled="fieldsDisabled"
                                    >
                                    <template x-if="validation.project_name">
                                        <div class="text-red-500 text-md font-semibold text-[15px] mt-[-3px] mb-1" x-text="validation.project_name"></div>
                                    </template>
                                </div>
                                @if (isset($project))
                                    <div class="mt-1">
                                        <label class="inline-block form-label font-bold text-md text-primary_ui-50">{{ t('core.date') }}:</label>
                                        <span>{{ \Carbon\Carbon::parse($project['created_at'])->subDay()->format('d/m/Y') }}</span>
                                    </div>
                                @endif
                                <div class="flex gap-2 mt-2 profile-choice">
                                    <x-forms.custom-radio size="md" model="profile" value="residential" disabled="{{ $isInfo }}">{{ t('core.profile.residential') }}</x-forms.custom-radio>
                                    <x-forms.custom-radio size="md" model="profile" value="commercial" disabled="{{ $isInfo }}">{{ t('core.profile.commercial') }}</x-forms.custom-radio>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="subscription-information text-md leading-5 space-y-0.5 italic text-gray-500">
                        <p><span>{{ t('core.subscription.credits_year') }} :</span> {{ $subscription['creditsYear'] }}</p>
                        <p><span>{{ t('core.subscription.credits_month') }} :</span> {{ $subscription['creditsMonth'] }}</p>
                        <p><span>{{ t('core.subscription.created_month') }} :</span> {{ $subscription['projectsMonth'] }}</p>
                        <p><span>{{ t('core.subscription.balance_month') }} :</span> {{ $subscription['balanceMonth'] }}</p>
                    </div>
                </div>
                {{-- SAVE BUTTON (hide if viewing project) --}}
                <div class="absolute right-0 bottom-0 flex justify-end">
                    <button
                        type="submit"
                        :disabled="isLoading || fieldsDisabled"
                        class="text-white text-lg uppercase inline-block btn bg-primary_ui-50 hover:bg-primary_ui-50/90 hover:border-primary_ui-50/90 border-primary_ui-50 rounded-none min-w-[200px] font-semibold"
                    >
                        <span x-show="!isLoading">{{ t('core.project.save') }}</span>
                        <span x-show="isLoading">{{ t('core.project.saving') }}...</span>
                    </button>
                </div>
            </form>

            {{-- Project info mode: show at the bottom --}}
            @if (isset($project))
            <div class="mt-8">
                <h2 class="text-2xl font-bold mb-2 text-primary_ui-50">{{ t('core.project.info') }}</h2>
                <div class="border-2 border-primary_ui-50 rounded-sm bg-gray-100 p-6 flex justify-center items-center min-h-[200px] mb-6">
                    <div class="font-semibold uppercase text-primary_ui-50 text-2xl">{{ t('core.under_construction') }} </div>
                </div>
                <div class="flex gap-4 justify-end mb-10">
                    <a href="/dashboard"
                       class="btn rounded-md w-52 text-lg uppercase bg-primary_ui-50 text-white font-bold py-2 px-6 hover:bg-primary_ui-50/90">
                       {{ t('core.dashboard.title') }}
                    </a>
                    <a href="/project/{{$project['id']}}/simulation"
                       class="btn rounded-md w-52 text-lg uppercase bg-primary_ui-50 text-white font-bold py-2 px-6 hover:bg-primary_ui-50/90">
                       {{ t('core.simulation.create') }}
                    </a>
                    {{-- <button
                        type="button"
                        class="btn rounded-md w-52 uppercase bg-primary_ui-50 text-white font-bold py-2 px-6 hover:bg-primary_ui-50/90"
                    >
                    {{ t('core.simulation.create') }}
                    </button> --}}
                </div>
            </div>
            @endif


            <div
                x-show="showConfirmationScreen"
                x-cloak
                x-transition
                class="fixed inset-0 z-50 flex items-start justify-center bg-white"
            >
                <div class="min-h-[340px] mt-[100px] text-center bg-white shadow-xl px-8 py-10 max-w-[800px] w-full relative">
                    <h2 class="font-bold">
                        {{ t('core.project.registration') }} <span class="uppercase text-primary_ui-50" x-text="profile"></span>
                    </h2>
                    <h3 class="text-primary_ui-50 mb-6 font-semibold" x-text="'Project ' + project_name.toUpperCase()"></h3>

                    <div class="text-gray-500 space-y-2 text-center">
                        <p>{{ t('core.subscription.credits_year') }}: {{ $subscription['creditsYear'] }}</p>
                        <p>{{ t('core.subscription.credits_month') }}: {{ $subscription['creditsMonth'] }}</p>
                        <p>{{ t('core.subscription.created_month') }}: {{ $subscription['projectsMonth'] }}</p>
                        <p>{{ t('core.subscription.balance_month') }}: {{ $subscription['balanceMonth'] }}</p>
                    </div>

                    <!-- Error/Success feedback -->
                    <template x-if="error">
                        <div class="text-red-600 mt-4 font-semibold text-center" x-text="error"></div>
                    </template>
                    <template x-if="success">
                        <div class="text-green-600 mt-4 font-semibold text-center" x-text="success"></div>
                    </template>

                    <div class="mt-6 flex bottom-0 right-0 gap-5 absolute">
                        <button @click="showConfirmationScreen = false"
                            class="text-black underline font-semibold hover:underline">
                            {{ t('core.cancel') }}
                        </button>
                        <button @click="await saveProject()" :disabled="isLoading"
                            class="bg-primary_ui-50 text-white uppercase px-4 min-w-[200px] py-2 font-bold shadow hover:bg-primary_ui-50/90">
                            <span x-show="!isLoading">{{ t('core.save') }}</span>
                            <span x-show="isLoading">{{ t('core.project.saving') }}...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </x-slot>
</x-layouts.dashboard>
