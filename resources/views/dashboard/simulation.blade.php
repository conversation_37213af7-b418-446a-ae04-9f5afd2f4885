<x-layouts.dashboard :styles="$styles">
    <x-slot name="content">
        <div class="flex flex-col gap-8 justify-center items-center min-h-[80vh]">
            <img
                data-src="{{ URL::asset('/images/under-construction.png') }}"
                src="{{ URL::asset('/images/under-construction.png') }}"
                class="w-64 md:w-80 mx-auto lazyLoad">
            <div class="font-semibold uppercase text-primary_ui-50 text-4xl">
                {{ t('core.under_construction') }}
            </div>
            <!-- Loading Spinner -->
            <div id="view-pdf-loading" class="flex flex-col items-center gap-2 mt-8 hidden">
                <svg class="animate-spin h-8 w-8 text-primary_ui-50" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
                <span class="text-primary_ui-50"> {{ t('core.pdf.generation_loading') }} </span>
            </div>
            <!-- View PDF Button -->
            <button id="view-pdf-btn" class="mt-8 px-6 py-2 bg-primary_ui-50 text-white rounded hover:bg-primary_ui-60 transition">
                {{ t('core.simulation.view_pdf') }}
            </button>
        </div>
        <script>
            const viewBtn = document.getElementById('view-pdf-btn');
            const loadingDiv = document.getElementById('view-pdf-loading');
            viewBtn.addEventListener('click', async function() {
                // Show loading, hide button
                loadingDiv.classList.remove('hidden');
                viewBtn.classList.add('hidden');
                const data = {
                    title: 'Simulation Report',
                    user: @json(auth()->user()->name ?? 'Guest'),
                    date: new Date().toLocaleDateString(),
                    lang: @json(app()->getLocale()),
                };
                try {
                    const response = await fetch('/api/project-items/generate-pdf', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/pdf',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(data)
                    });
                    if (!response.ok) throw new Error('Failed to generate PDF');
                    const blob = await response.blob();
                    // Store PDF blob in sessionStorage as base64 for the editor page
                    const reader = new FileReader();
                    reader.onloadend = function() {
                        sessionStorage.setItem('simulationPdf', reader.result);
                        // Redirect to the PDF editor view
                        // window.location.href = `/project/${@json($project->id ?? '1')}/simulation/pdf`;
                        window.open(`/project/${@json($project->id ?? '1')}/simulation/pdf`, '_blank');
                    };
                    reader.readAsDataURL(blob);
                } catch (err) {
                    alert('Error generating PDF: ' + err.message);
                } finally {
                    loadingDiv.classList.add('hidden');
                    viewBtn.classList.remove('hidden');
                }
            });
        </script>
    </x-slot>
</x-layouts.dashboard>
