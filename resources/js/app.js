import Alpine from 'alpinejs'
import intersect from '@alpinejs/intersect'
// plugins have to be imported before Alpine is started
Alpine.plugin(intersect)

document.addEventListener('DOMContentLoaded', function () {
    assignTabSliderEvents();
    lazyLoadImage();
});

function assignTabSliderEvents() {
    // do that for each .tab-slider
    let tabSliders = document.querySelectorAll(".tab-slider")

    tabSliders.forEach(tabSlider => {
        let tabs = tabSlider.querySelectorAll(".tab")
        let panels = tabSlider.querySelectorAll(".tab-panel")

        tabs.forEach(tab => {
            tab.addEventListener("click", ()=>{
                let tabTarget = tab.getAttribute("aria-controls")
                // set all tabs as not active
                tabs.forEach(tab =>{
                    tab.setAttribute("data-active-tab", "false")
                    tab.setAttribute("aria-selected", "false")
                })

                // set the clicked tab as active
                tab.setAttribute("data-active-tab", "true")
                tab.setAttribute("aria-selected", "true")

                panels.forEach(panel =>{
                    let panelId = panel.getAttribute("id")
                    if(tabTarget === panelId){
                        panel.classList.remove("hidden", "opacity-0")
                        panel.classList.add("block", "opacity-100")
                        // animate panel fade in

                        panel.animate([
                            { opacity: 0, maxHeight: 0 },
                            { opacity: 1, maxHeight: "100%" }
                        ], {
                            duration: 500,
                            easing: "ease-in-out",
                            fill: "forwards"
                        })

                    } else {
                        panel.classList.remove("block", "opacity-100")
                        panel.classList.add("hidden", "opacity-0")

                        // animate panel fade out
                        panel.animate([
                            { opacity: 1, maxHeight: "100%" },
                            { opacity: 0, maxHeight: 0 }
                        ], {
                            duration: 500,
                            easing: "ease-in-out",
                            fill: "forwards"
                        })
                    }
                })
            })
        })

        let activeTab = tabSlider.querySelector(".tab[data-active-tab='true']")
        activeTab.click()
    })

}

function lazyLoadImage(){
    const lazyLoadClass = "lazy-load-img"
    const ImageToLazyLoad = ".lazyLoad"
    const preloadImage = (img) => {
        img.src = img.dataset ? img.dataset.src : img.getAttribute("data-src");
        img.addClass
        img.onload = ()=>{
            img.classList ? img.classList.remove("lazy-load-img") : img.className = img.className.replace(/\blazy-load-img\b/g, "").replace(/\s+/g, " ").trim();
        }
    }

    const configOptions = {
        rootMargin: "10px"
    };

    const observer = new IntersectionObserver(function(entries, self) {
        for(let entry of entries) {
            if(entry.isIntersecting){
                let elem = entry.target;
                preloadImage(elem);
                self.unobserve(elem);
            }
        }
    }, configOptions);

    let images = document.querySelectorAll(ImageToLazyLoad);

    for(let image of images){
        image.src = "https://cdn.pvgis.com/images/loading-ps.svg";
        image.classList ? image.classList.add(lazyLoadClass) : image.className = image.className.trim()+" lazy-load-img";
        observer.observe(image);
    }
}
