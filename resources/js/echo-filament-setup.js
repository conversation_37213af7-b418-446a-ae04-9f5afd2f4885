// resources/js/echo-filament-setup.js
import <PERSON> from 'laravel-echo';
import Pusher from 'pusher-js'; // Pusher-JS is used by Echo for the Pusher broadcaster
import axios from 'axios';

const REVERB_APP_KEY = '__REVERB_APP_KEY__';
const REVERB_HOST = '__REVERB_HOST__';
const REVERB_PORT = '__REVERB_PORT__'; // This will be a string from Rollup
const REVERB_SCHEME = '__REVERB_SCHEME__';
const REVERB_APP_CLUSTER = '__REVERB_APP_CLUSTER__';

// Make Pusher globally available if Echo needs it (often does for 'pusher' broadcaster)
window.Pusher = Pusher;

const csrfTokenMeta = document.querySelector('meta[name="csrf-token"]');
let csrfToken = null;

if (csrfTokenMeta) {
  csrfToken = csrfTokenMeta.getAttribute('content');
} else {
  console.warn(
    'Filament Echo (Module): CSRF token meta tag not found. Broadcasting auth for private channels may fail.',
  );
}

// Check if an Echo instance already exists and if Reverb config is available via Vite's import.meta.env
if (
  typeof window.Echo === 'undefined' &&
  REVERB_APP_KEY // Vite injects these from .env
) {
  console.log(
    'Filament Echo (Module): Attempting to initialize Echo with key:',
    REVERB_APP_KEY,
  );
  try {
    window.Echo = new Echo({
      broadcaster: 'pusher', // CRITICAL: Must be 'pusher' for Reverb client
      key: REVERB_APP_KEY,
      wsHost: REVERB_HOST,
      wsPort: parseInt(REVERB_PORT || '80', 10),
      wssPort: parseInt('443', 10), // Use same port or default 443 for WSS
      forceTLS: REVERB_SCHEME === 'https',
      enabledTransports: ['ws', 'wss'],
      cluster: REVERB_APP_CLUSTER || 'mt1', // CRITICAL: Add cluster, use VITE_PUSHER_APP_CLUSTER if defined, else 'mt1'

      // Default auth configuration for Echo.
      authEndpoint: '/broadcasting/auth',
      auth: {
        headers: {
          'X-CSRF-TOKEN': csrfToken,
          Accept: 'application/json',
        },
      },

      authorizer: (channel, options) => {
        console.log(
          `%cFilament Echo Authorizer (Module): OUTER function invoked for channel: ${channel.name}`,
          'color: blue; font-weight: bold;',
          options,
        );
        return {
          authorize: (socketId, callback) => {
            console.log(
              `%cFilament Echo Authorizer (Module): INNER authorize method invoked. Attempting to POST for channel: ${channel.name}, socket ID: ${socketId}`,
              'color: green; font-weight: bold;',
            );
            console.log('CSRF Token for auth POST:', csrfToken);
            console.log('Socket ID for auth POST:', socketId);
            console.log('Channel Name for auth POST:', channel.name);
            console.log('Type of axios before POST:', typeof axios);

            if (typeof axios !== 'function') {
                console.error('%cFilament Echo Authorizer (Module): axios is not a function! Cannot make POST request.', 'color: red;');
                callback(new Error('Axios is not available'), null);
                return;
            }

            axios
              .post(
                '/broadcasting/auth',
                {
                  socket_id: socketId,
                  channel_name: channel.name,
                },
                {
                  headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    Accept: 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                  },
                },
              )
              .then((response) => {
                console.log(
                  `Filament Echo Authorizer (Module): Auth successful for ${channel.name}`,
                  response.data,
                );
                callback(null, response.data);
              })
              .catch((error) => {
                console.error(
                  `Filament Echo Authorizer (Module): Auth failed for ${channel.name}`,
                  error.response ? error.response.data : error.message,
                  error.config
                );
                callback(error, null);
              });
          },
        };
      },
    });

    console.log(
      '%cFilament Echo (Module): Echo object INSTANCE CREATED.',
      'color: #4CAF50; font-weight: bold;',
      window.Echo,
    );

    if (window.Echo && window.Echo.connector && window.Echo.connector.pusher) {
      const pusherConnection = window.Echo.connector.pusher.connection;
      console.log('Filament Echo (Module): Setting up Pusher connection listeners.');
      // ... (add your pusherConnection.bind listeners here for connect, error, etc.)
        pusherConnection.bind('connecting', () => { console.log('%cFilament Echo (Pusher - Module): Connecting...', 'color: orange;'); });
        pusherConnection.bind('connected', () => { console.log('%cFilament Echo (Pusher - Module): Successfully CONNECTED!', 'color: green;'); });
        pusherConnection.bind('error', (err) => { console.error('%cFilament Echo (Pusher - Module): Connection ERROR:', 'color: red;', err); });
    } else {
      console.error('Filament Echo (Module): Echo connector or pusher instance not found after init.');
    }

  } catch (e) {
    console.error('%cFilament Echo (Module): ERROR during Echo initialization:', 'color: red; font-weight: bold;', e);
  }
} else if (typeof window.Echo !== 'undefined') {
  console.info('%cFilament Echo (Module): Echo INSTANCE already exists.', 'color: #2196F3;');
} else {
  console.warn(
    '%cFilament Echo (Module): Reverb VITE_ variables not set or empty. Echo not initialized.',
    'color: #FF9800;',
  );
}
