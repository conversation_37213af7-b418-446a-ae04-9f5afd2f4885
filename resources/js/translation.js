function getCurrentLanguage() {
  return window.env.CURRENT_LANGUAGE || 'en';
}

async function translateUrl(urls, currentLanguage) {
  if (urls.length === 0 ) return urls;

  const { TRANSLATION_API_URL,APP_URL } = window.env;

  const urlStr = urls.map((url) => decodeURIComponent(url.replace(APP_URL, "").replace(/#/g, "")));
    
  try {
    const encodedUrlStr = encodeURIComponent(JSON.stringify(urlStr || []));
    const response = await fetch(
      `${TRANSLATION_API_URL}/translation/${currentLanguage}/translateUrls?v=${window.env.RESOURCE_VERSION}&url=${encodedUrlStr}`,
      {
        method: "GET",
        cache: "force-cache",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (response.status === 200||response.status === 201) {
      return (await response.json()).map(r=>r.path);
    }
  } catch (ex) { }
  return [];
}

async function translateImageUrls(urls, currentLanguage) {
  const imagePattern = /^https:\/\/cdn\.pvgis\.com\/images\/(.+)\/([^/]+)\/(\d+)-([^/]+)\.(jpg|png|gif|webp|jpeg|svg|bmp)(\?.*)?$/i;
  
  // Group URLs by folder
  const folderGroups = new Map();
  
  urls.forEach(url => {
    const match = url.match(imagePattern);
    if (match) {
      const folderName = match[1];
      match[3];
      const imageName = `${match[3]}-${match[4]}.${match[5]}`;
      
      if (!folderGroups.has(folderName)) {
        folderGroups.set(folderName, new Set());
      }
      folderGroups.get(folderName).add(imageName);
    }
  });

  // old translation url image process
  if (folderGroups.size === 0) {
    return new Map(urls.map(url => [url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`)]));
  }

  try {
    const response = await fetch(`${window.env.CDN_URL}/translate_url_image.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        folder: Array.from(folderGroups.entries()).map(([name, images]) => ({
          name,
          images: Array.from(images)
        })),
        lang: currentLanguage
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      const urlMap = new Map();
      
      // Create a map of original URLs to translated URLs
      urls.forEach(url => {
        const match = url.match(imagePattern);
        if (match) {
          const folderName = match[1];
          const imageId = match[3];
          
          const resFolder = data?.find(folder => folder.name === folderName);
          const translatedUrl = resFolder?.images?.find(image => image.id === imageId);
          
          if (translatedUrl?.url) {
            urlMap.set(url, `${translatedUrl.url}?v=${window.env.RESOURCE_VERSION}`);
          } else {
            urlMap.set(url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`));
          }
        } else {
          urlMap.set(url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`));
        }
      });
      
      return urlMap;
    }
  } catch (error) {
    console.error('Error translating image URLs:', error);
  }
  
  // If request fails, return a map with just language updates
  return new Map(urls.map(url => [url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`)]));
}

async function applyURLTranslation(
  currentLanguage = getCurrentLanguage(),
) {
  try {
    const cmsLink = document.querySelectorAll("li[data-cms-link] a, a[data-cms-link]");
    const translatedUrl = await translateUrl(Array.from(cmsLink.values()).map(link => link.href), currentLanguage);
    
    translatedUrl.forEach((url, i) => {
      if (url !== "/not-found" && url !== "/undefined") {
        cmsLink[i].href = url;
      }
    });

    const imgWithCmsLink = document.querySelectorAll("img[data-cms-link]");
    const imageUrls = Array.from(imgWithCmsLink).map(img => {
      const src = img.getAttribute('src') && img.getAttribute('src') != `${window.env.CDN_URL}/images/loading-ps.svg` ?
        img.getAttribute('src') : img.getAttribute('data-src');
      return src;
    }).filter(Boolean);

    if (imageUrls.length > 0) {
      const translatedUrls = await translateImageUrls(imageUrls, currentLanguage);
      
      imgWithCmsLink.forEach(img => {
        const src = img.getAttribute('src') && img.getAttribute('src') != `${window.env.CDN_URL}/images/loading-ps.svg` ?
          img.getAttribute('src') : img.getAttribute('data-src');
          
        if (src && translatedUrls.has(src)) {
          const translatedSrc = translatedUrls.get(src);
          img.setAttribute('src', translatedSrc);
          if (img.hasAttribute('data-src')) {
            img.setAttribute('data-src', translatedSrc);
          }
        }
      });
    }
  } catch (error) {
    console.error("Error in applyURLTranslation:", error);
  } finally {
    // hideLoader();
  }
}

document.addEventListener('DOMContentLoaded', function () {
  applyURLTranslation();
});