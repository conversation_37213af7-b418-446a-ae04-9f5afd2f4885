{"private": true, "type": "module", "scripts": {"dev": "vite", "start": "composer run dev", "build": "vite build && npm run build:filament-echo", "build:filament-echo": "node ./scripts/build-filament-echo.js"}, "devDependencies": {"@popperjs/core": "^2.11.6", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "axios": "^1.9.0", "concurrently": "^9.0.1", "daisyui": "^4.9.0", "dotenv": "^16.5.0", "laravel-echo": "^2.1.4", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.32", "pusher-js": "^8.4.0", "rollup": "^4.41.0", "sass-embedded": "^1.85.1", "tailwindcss": "^3.4.3", "vite": "^5.0.0", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-static-copy": "^3.1.0"}, "dependencies": {"@alpinejs/intersect": "^3.13.3", "@codemirror/lang-html": "^6.4.9", "alpinejs": "^3.13.3", "clipboard": "^2.0.11", "codemirror": "^6.0.1", "highlight.js": "^11.9.0", "monaco-editor": "^0.52.2"}}