import { rollup } from 'rollup';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import replace from '@rollup/plugin-replace';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config(); 

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const inputFile = path.resolve(
  __dirname,
  '../resources/js/echo-filament-setup.js',
);

const outputFile = path.resolve(
  __dirname,
  '../public/js/app/echo-filament-setup.js',
); 

async function build() {
  console.log('Building Filament Echo (non-module) setup script...');

  const replacements = {
    preventAssignment: true,
    __REVERB_APP_KEY__: process.env.REVERB_APP_KEY || '',
    __REVERB_HOST__: process.env.REVERB_HOST || 'localhost',
    __REVERB_PORT__: process.env.REVERB_PORT || '8002',
    __REVERB_SCHEME__: process.env.REVERB_SCHEME || 'http',
  };

  try {
    const bundle = await rollup({
      input: inputFile,
      plugins: [
        resolve({ browser: true }),
        commonjs(),
        replace(replacements),
      ],
      context: 'window', 
    });

    console.log("bundle created")

    await bundle.write({
      file: outputFile,
      format: 'iife', 
      sourcemap: process.env.NODE_ENV !== 'production' ? 'inline' : false,
    });

    console.log(
      `Filament Echo script built successfully`,
    );
  } catch (error){
    console.error(
      'Error building Filament Echo (non-module) script:',
      error,
    );
    process.exit(1);
  }
}

build();
